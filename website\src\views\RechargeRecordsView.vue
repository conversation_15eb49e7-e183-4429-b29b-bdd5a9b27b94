<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">充值记录</h1>
        <p class="mt-2 text-gray-600">查看您的所有充值记录和交易详情</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- 充值记录列表 -->
      <div v-else-if="records.length > 0" class="bg-white rounded-lg shadow-sm overflow-hidden">
        <!-- 表格头部 -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-500 uppercase tracking-wider">
            <div class="col-span-3">交易信息</div>
            <div class="col-span-2">充值金额</div>
            <div class="col-span-2">支付方式</div>
            <div class="col-span-2">交易状态</div>
            <div class="col-span-2">充值时间</div>
            <div class="col-span-1">操作</div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="divide-y divide-gray-200">
          <div 
            v-for="record in records" 
            :key="record.id"
            class="px-6 py-4 hover:bg-gray-50 transition-colors"
          >
            <div class="grid grid-cols-12 gap-4 items-center">
              <!-- 交易信息 -->
              <div class="col-span-3">
                <div class="font-medium text-gray-900">贝壳充值</div>
                <div class="flex items-center text-sm text-gray-500">
                  <span>{{ record.transactionNo }}</span>
                  <button
                    @click="copyToClipboard(record.transactionNo, '订单号')"
                    class="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="复制订单号"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 充值金额 -->
              <div class="col-span-2">
                <div class="font-medium text-gray-900">{{ formatNumber(record.amount) }} 贝壳</div>
                <div class="text-sm text-gray-500">充值金额</div>
              </div>

              <!-- 支付方式 -->
              <div class="col-span-2">
                <div class="flex items-center">
                  <div class="w-6 h-6 mr-2">
                    <img v-if="extractPaymentMethod(record.description) === 'alipay'" src="/images/alipay-icon.svg" alt="支付宝" class="w-full h-full">
                    <img v-else-if="extractPaymentMethod(record.description) === 'wechat'" src="/images/wechat-icon.svg" alt="微信支付" class="w-full h-full">
                    <img v-else-if="extractPaymentMethod(record.description) === 'bank'" src="/images/bank-icon.svg" alt="银行卡" class="w-full h-full">
                    <div v-else class="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                      <span class="text-xs text-gray-500">💳</span>
                    </div>
                  </div>
                  <span class="text-sm text-gray-900">{{ getPaymentMethodName(extractPaymentMethod(record.description)) }}</span>
                </div>
              </div>

              <!-- 交易状态 -->
              <div class="col-span-2">
                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full" :class="getStatusClass(record.status)">
                  {{ getStatusText(record.status) }}
                </span>
                <div class="text-xs text-gray-500 mt-1" v-if="record.status === 'failed' && record.failReason">
                  {{ record.failReason }}
                </div>
              </div>

              <!-- 充值时间 -->
              <div class="col-span-2">
                <div class="text-sm text-gray-900">{{ formatDate(record.createdAt) }}</div>
                <div class="text-xs text-gray-500" v-if="record.completedAt">
                  完成: {{ formatTime(record.completedAt) }}
                </div>
              </div>

              <!-- 操作 -->
              <div class="col-span-1">
                <button 
                  v-if="record.status === 'pending'"
                  @click="checkPaymentStatus(record.id)"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  查询
                </button>
                <button 
                  v-else-if="record.status === 'completed'"
                  @click="viewReceipt(record)"
                  class="text-green-600 hover:text-green-800 text-sm font-medium"
                >
                  凭证
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} - 
              {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，
              共 {{ pagination.total }} 条记录
            </div>
            <div class="flex space-x-2">
              <button 
                @click="changePage(pagination.page - 1)"
                :disabled="pagination.page <= 1"
                class="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                上一页
              </button>
              <button 
                @click="changePage(pagination.page + 1)"
                :disabled="pagination.page >= pagination.totalPages"
                class="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="bg-white rounded-lg shadow-sm p-12 text-center">
        <div class="text-gray-400 text-6xl mb-4">💳</div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无充值记录</h3>
        <p class="text-gray-500 mb-6">您还没有进行任何充值，快去充值贝壳开始投资吧！</p>
        <router-link 
          to="/dashboard" 
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          去充值
        </router-link>
      </div>

      <!-- 返回按钮 -->
      <div class="mt-8 text-center">
        <button 
          @click="$router.back()" 
          class="inline-flex items-center px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          ← 返回
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { formatNumber } from '../utils/format'
import { getRechargeRecords } from '../api/wallet'
import type { RechargeRecord } from '../types'
import Swal from 'sweetalert2'

// 数据状态
const loading = ref(true)
const records = ref<RechargeRecord[]>([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 获取充值记录
const fetchRechargeRecords = async (page = 1) => {
  try {
    loading.value = true
    const response = await getRechargeRecords(page, pagination.value.pageSize)

    if (response.success) {
      records.value = response.data.records
      pagination.value = response.data.pagination
    } else {
      console.error('获取充值记录失败:', response.message)
    }
  } catch (error) {
    console.error('获取充值记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page: number) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    fetchRechargeRecords(page)
  }
}

// 获取支付方式名称
const getPaymentMethodName = (method: string) => {
  const names: Record<string, string> = {
    'alipay': '支付宝',
    'wechat': '微信支付',
    'bank': '银行卡',
    'balance': '余额支付'
  }
  return names[method] || '未知'
}

// 从描述中提取支付方式
const extractPaymentMethod = (description: string): string => {
  if (description.includes('支付宝')) return 'alipay'
  if (description.includes('微信')) return 'wechat'
  if (description.includes('银行卡')) return 'bank'
  return 'unknown'
}

// 获取状态样式
const getStatusClass = (status: RechargeRecord['status']) => {
  const classes: Record<RechargeRecord['status'], string> = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'completed': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'cancelled': 'bg-gray-100 text-gray-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

// 获取状态文本
const getStatusText = (status: RechargeRecord['status']) => {
  const texts: Record<RechargeRecord['status'], string> = {
    'pending': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return texts[status] || '未知'
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

// 格式化时间
const formatTime = (dateString: string) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

// 查询支付状态
const checkPaymentStatus = async (recordId: number) => {
  try {
    console.log('查询支付状态:', recordId)
    // 重新获取记录列表以更新状态
    await fetchRechargeRecords(pagination.value.page)
  } catch (error) {
    console.error('查询支付状态失败:', error)
  }
}

// 查看凭证
const viewReceipt = (record: RechargeRecord) => {
  console.log('查看凭证:', record)
  // 可以在这里实现查看凭证的逻辑，比如打开弹窗显示交易详情
  alert(`交易凭证\n\n交易号: ${record.transactionNo}\n金额: ${formatNumber(record.amount)} 贝壳\n状态: ${getStatusText(record.status)}\n时间: ${formatDate(record.createdAt)}`)
}

// 复制到剪贴板
const copyToClipboard = async (text: string, type: string) => {
  try {
    await navigator.clipboard.writeText(text)

    // 显示成功提示
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 2000,
      timerProgressBar: true
    })

    Toast.fire({
      icon: 'success',
      title: `${type}已复制到剪贴板`
    })
  } catch (error) {
    console.error('复制失败:', error)

    // 显示失败提示
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 2000,
      timerProgressBar: true
    })

    Toast.fire({
      icon: 'error',
      title: '复制失败，请手动复制'
    })
  }
}

// 组件挂载时加载数据
onMounted(() => {
  fetchRechargeRecords()
})
</script>
