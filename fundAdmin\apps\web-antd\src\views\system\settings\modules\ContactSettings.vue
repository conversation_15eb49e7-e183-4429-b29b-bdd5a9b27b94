<template>
  <div class="settings-container">
    <a-form
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      @finish="handleSubmit"
      class="settings-form"
      layout="horizontal"
    >
      <!-- 二维码上传区域 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">二维码设置</h3>
        
        <a-form-item
          label="在线联系二维码"
          name="onlineQrCode"
          class="form-item-visible"
        >
          <SiteAssetUpload
            v-model="formState.onlineQrCode"
            asset-type="qrcode"
            @upload-success="handleOnlineQrCodeUploadSuccess"
            @upload-error="handleUploadError"
          />
          <div class="text-xs text-gray-500 mt-1">
            用于客服咨询等在线联系方式，建议尺寸：200x200px
          </div>
        </a-form-item>

        <a-form-item
          label="线下支付二维码"
          name="paymentQrCode"
          class="form-item-visible"
        >
          <SiteAssetUpload
            v-model="formState.paymentQrCode"
            asset-type="qrcode"
            @upload-success="handlePaymentQrCodeUploadSuccess"
            @upload-error="handleUploadError"
          />
          <div class="text-xs text-gray-500 mt-1">
            用于线下支付场景，建议尺寸：200x200px
          </div>
        </a-form-item>
      </div>

      <!-- 联系信息区域 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">联系信息</h3>
        
        <a-form-item
          label="联系我们文字"
          name="contactText"
          :rules="[
            { max: 500, message: '联系我们文字不能超过500个字符' }
          ]"
          class="form-item-visible"
        >
          <a-textarea
            v-model:value="formState.contactText"
            :rows="3"
            placeholder="请输入联系我们的描述文字，用于页脚展示"
            class="textarea-visible"
          />
        </a-form-item>

        <a-form-item
          label="联系地址"
          name="contactAddress"
          :rules="[
            { max: 200, message: '联系地址不能超过200个字符' }
          ]"
          class="form-item-visible"
        >
          <a-input
            v-model:value="formState.contactAddress"
            placeholder="请输入公司或机构的物理地址"
            class="input-visible"
          />
        </a-form-item>

        <a-form-item
          label="联系邮箱"
          name="contactEmail"
          :rules="[
            { type: 'email', message: '请输入正确的邮箱格式' },
            { max: 100, message: '邮箱地址不能超过100个字符' }
          ]"
          class="form-item-visible"
        >
          <a-input
            v-model:value="formState.contactEmail"
            placeholder="请输入客服或商务邮箱地址"
            class="input-visible"
          />
        </a-form-item>

        <a-form-item
          label="联系电话"
          name="contactPhone"
          :rules="[
            { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$|^400[-\s]?\d{3}[-\s]?\d{4}$/, message: '请输入正确的电话号码格式' },
            { max: 25, message: '电话号码不能超过25个字符' }
          ]"
          class="form-item-visible"
        >
          <a-input
            v-model:value="formState.contactPhone"
            placeholder="请输入客服或商务联系电话，如：400 2130 0084"
            class="input-visible"
          />
        </a-form-item>
      </div>

      <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
        <div class="flex justify-end">
          <a-button type="primary" html-type="submit" :loading="saving">
            {{ saving ? '保存中...' : '保存设置' }}
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import {
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Textarea as ATextarea,
  Button as AButton,
  message
} from 'ant-design-vue';
import { getContactSettings, updateContactSettings } from '#/api/system/settings';
import type { ContactSettings } from '#/api/system/settings/types';
import SiteAssetUpload from '../components/SiteAssetUpload.vue';

// 表单状态
const formState = reactive<ContactSettings>({
  onlineQrCode: '',
  paymentQrCode: '',
  contactText: '',
  contactAddress: '',
  contactEmail: '',
  contactPhone: '',
});

// 保存状态
const saving = ref(false);

// 加载联系方式设置
const loadSettings = async () => {
  try {
    console.log('开始加载联系方式设置...');
    const data = await getContactSettings();
    console.log('联系方式设置API响应:', data);

    if (data) {
      console.log('联系方式设置数据:', data);
      Object.assign(formState, data);
      console.log('表单状态更新后:', formState);
    } else {
      console.warn('联系方式设置响应中没有数据');
    }
  } catch (error: any) {
    console.error('加载联系方式设置失败:', error);
    message.error(error.message || '加载联系方式设置失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  saving.value = true;
  try {
    console.log('提交联系方式设置:', formState);
    await updateContactSettings(formState);
    message.success('联系方式设置保存成功');
    // 重新加载数据以确保显示最新的设置
    await loadSettings();
  } catch (error: any) {
    console.error('保存联系方式设置失败:', error);
    message.error(error.message || '保存联系方式设置失败');
  } finally {
    saving.value = false;
  }
};

// 处理在线联系二维码上传成功
const handleOnlineQrCodeUploadSuccess = (data: any) => {
  console.log('在线联系二维码上传成功:', data);
  // formState.onlineQrCode 已经通过 v-model 自动更新
};

// 处理线下支付二维码上传成功
const handlePaymentQrCodeUploadSuccess = (data: any) => {
  console.log('线下支付二维码上传成功:', data);
  // formState.paymentQrCode 已经通过 v-model 自动更新
};

// 处理上传错误
const handleUploadError = (error: Error) => {
  console.error('上传失败:', error);
  // 错误信息已经在组件内部显示，这里可以做额外处理
};

// 组件挂载时加载数据
onMounted(() => {
  loadSettings();
});
</script>

<style scoped>
@import '../styles/common.css';

.settings-container h3 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}
</style>
