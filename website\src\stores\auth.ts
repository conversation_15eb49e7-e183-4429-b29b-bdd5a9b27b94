/**
 * 认证状态管理 Store
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, logout as apiLogout } from '../api/auth'
import type { LoginCredentials, User } from '../types/auth'
import { addDeviceLoginRecord, isNewDevice, saveDeviceInfo } from '../utils/deviceUtils'
import { securityLogger, detectSuspiciousActivity } from '../utils/securityLogger'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)
  const rememberMe = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!(user.value && token.value))
  const userInfo = computed(() => user.value)

  // 初始化认证状态
  const initAuth = () => {
    try {
      // 检查记住登录状态
      const savedRememberMe = localStorage.getItem('rememberMe') === 'true'
      rememberMe.value = savedRememberMe

      // 根据记住登录状态选择存储方式
      const storage = savedRememberMe ? localStorage : sessionStorage

      const savedToken = storage.getItem('token')
      const savedUser = storage.getItem('user')

      if (savedToken && savedUser) {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      clearAuth()
    }
  }

  // 登录
  const login = async (credentials: LoginCredentials & { rememberMe?: boolean }) => {
    try {
      isLoading.value = true

      // 检测可疑活动
      detectSuspiciousActivity()

      const response = await apiLogin(credentials)

      if (response.success && response.data) {
        token.value = response.data.token
        user.value = response.data.user
        rememberMe.value = credentials.rememberMe || false

        // 根据记住登录状态选择存储方式
        const storage = rememberMe.value ? localStorage : sessionStorage

        // 保存到存储
        storage.setItem('token', response.data.token)
        storage.setItem('user', JSON.stringify(response.data.user))
        localStorage.setItem('rememberMe', rememberMe.value.toString())

        // 保存设备信息
        saveDeviceInfo()

        // 记录设备登录
        const isNew = isNewDevice()
        addDeviceLoginRecord()

        // 记录安全日志
        await securityLogger.login({
          userId: response.data.user.id,
          username: response.data.user.username,
          isNewDevice: isNew,
          rememberMe: rememberMe.value
        })

        return response
      } else {
        // 记录登录失败
        await securityLogger.loginFailed({
          username: credentials.username,
          reason: response.message || '登录失败'
        })
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)

      // 记录登录失败（如果还没记录）
      if (!error.message?.includes('登录失败')) {
        await securityLogger.loginFailed({
          username: credentials.username,
          error: error.message
        })
      }

      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      isLoading.value = true

      // 记录登出日志
      if (user.value) {
        await securityLogger.logout({
          userId: user.value.id,
          username: user.value.username
        })
      }

      // 调用后端登出API
      if (token.value) {
        await apiLogout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地状态
      clearAuth()
      isLoading.value = false
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = null
    rememberMe.value = false

    // 清除所有存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('rememberMe')
    sessionStorage.removeItem('token')
    sessionStorage.removeItem('user')
  }

  // 更新用户信息
  const updateUser = (newUser: User) => {
    user.value = newUser

    // 根据记住登录状态选择存储方式
    const storage = rememberMe.value ? localStorage : sessionStorage
    storage.setItem('user', JSON.stringify(newUser))
  }

  // 检查令牌是否有效
  const checkTokenValidity = () => {
    if (!token.value) return false
    
    try {
      // 简单的令牌格式检查
      const parts = token.value.split('.')
      if (parts.length !== 3) return false
      
      // 解析payload检查过期时间
      const payload = JSON.parse(atob(parts[1]))
      const now = Math.floor(Date.now() / 1000)
      
      if (payload.exp && payload.exp < now) {
        clearAuth()
        return false
      }
      
      return true
    } catch (error) {
      console.error('令牌验证失败:', error)
      clearAuth()
      return false
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    rememberMe,

    // 计算属性
    isLoggedIn,
    userInfo,

    // 方法
    initAuth,
    login,
    logout,
    clearAuth,
    updateUser,
    checkTokenValidity
  }
})
