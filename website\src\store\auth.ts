// src/store/auth.ts
import { defineStore } from 'pinia'
import { logout as logoutApi, invalidateAllTokens } from '../api/auth'
import Swal from 'sweetalert2'
import { clearAuthCookies } from '../utils/cookieUtils'
import router from '../router'
import type { User } from '../types'
import { initWebSocket, closeWebSocket } from '../utils/websocket'

// 认证状态接口
export interface AuthState {
  user: User | null
  token: string | null
  isLoggedIn: boolean
}

// Cookie选项类型
interface CookieOptions {
  path: string
  secure: boolean
  sameSite: 'Strict' | 'Lax' | 'None'
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: null,
    isLoggedIn: false
  }),

  getters: {
    // 获取用户信息
    getUser: (state): User | null => state.user,

    // 判断是否登录
    getIsLoggedIn: (state): boolean => state.isLoggedIn,

    // 获取用户类型
    getUserType: (state): string | null => state.user?.user_type || null,

    // 判断是否为投资者
    isInvestor: (state): boolean => state.user?.user_type === 'investor',

    // 判断是否为承制厂牌
    isProducer: (state): boolean => state.user?.user_type === 'producer',

    // 判断是否为基金管理员
    isFundManager: (state): boolean => state.user?.user_type === 'fund_manager'
  },

  actions: {
    // 初始化状态（从localStorage加载）
    initAuth(): void {
      try {
        const token = localStorage.getItem('token')
        const userStr = localStorage.getItem('user')

        if (token && userStr) {
          const user: User = JSON.parse(userStr)
          this.setAuthState(user, token)

          // 初始化WebSocket连接
          initWebSocket(token, 'user').catch(error => {
            console.error('初始化WebSocket失败:', error)
          })
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error)
        this.clearAuth()
      }
    },
    
    // 设置认证状态
    setAuthState(user: User, token: string): void {
      this.token = token
      this.user = user
      this.isLoggedIn = !!user

      // 存储到localStorage
      if (user && token) {
        localStorage.setItem('user', JSON.stringify(user))
        localStorage.setItem('token', token)

        // 初始化WebSocket连接
        initWebSocket(token, 'user').catch(error => {
          console.error('WebSocket连接失败:', error)
        })
      }
    },
    
    // 清除认证状态
    clearAuth(): void {
      this.user = null
      this.token = null
      this.isLoggedIn = false

      // 关闭WebSocket连接
      closeWebSocket()

      // 清除localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('user')

      // 清除所有认证相关Cookie
      const cookieOptions: CookieOptions = {
        path: '/',
        secure: window.location.protocol === 'https:',
        sameSite: 'Strict'
      }
      clearAuthCookies([], cookieOptions)
    },
    
    // 设置token
    setToken(token: string): void {
      this.token = token
      localStorage.setItem('token', token)
      this.isLoggedIn = true
    },
    
    // 设置用户
    setUser(user: User): void {
      this.user = user
      localStorage.setItem('user', JSON.stringify(user))
    },
    
    // 登录
    login(userData: User, token: string): void {
      // 存储到localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(userData))
      
      // 更新状态
      this.setAuthState(userData, token)
    },
    
    // 退出登录 - 快速退出版本
    async logout(showToast: boolean = true): Promise<void> {
      try {
        // 设置标记，表示这是一次正常的退出登录操作
        sessionStorage.setItem('isLogout', 'true')

        // 如果需要显示提示，显示简短的加载状态
        if (showToast) {
          Swal.fire({
            title: '正在退出...',
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            timer: 500, // 缩短到0.5秒
            didOpen: () => {
              Swal.showLoading()
            }
          })
        }

        // 只在当前设备退出
        await logoutApi()

        // 清除认证状态
        this.clearAuth()

        // 关闭加载对话框
        if (showToast) {
          Swal.close()

          // 显示简短的成功提示
          const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 1000, // 缩短到1秒
            timerProgressBar: true
          })

          Toast.fire({
            icon: 'success',
            title: '您已安全退出'
          })
        }

        // 立即跳转到首页
        router.push('/')

      } catch (error) {
        console.error('退出登录错误:', error)

        // 无论API是否成功，都清除本地认证状态
        this.clearAuth()

        if (showToast) {
          // 显示简短的警告提示
          const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 1000, // 缩短到1秒
            timerProgressBar: true
          })

          Toast.fire({
            icon: 'warning',
            title: '本地登录状态已清除'
          })
        }

        // 立即跳转到首页
        router.push('/')
      }
    },

    // 快速退出（无提示）
    async quickLogout(): Promise<void> {
      await this.logout(false)
    }
  }
})
