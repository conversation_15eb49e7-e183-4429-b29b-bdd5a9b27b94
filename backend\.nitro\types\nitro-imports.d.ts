declare global {
  const LogLevel: typeof import('../../utils/logger')['LogLevel']
  const PERMISSIONS: typeof import('../../utils/permission')['PERMISSIONS']
  const addTokenToBlacklist: typeof import('../../utils/token-blacklist')['addTokenToBlacklist']
  const appendCorsHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['appendCorsHeaders']
  const appendCorsPreflightHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['appendCorsPreflightHeaders']
  const appendHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['appendHeader']
  const appendHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['appendHeaders']
  const appendResponseHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['appendResponseHeader']
  const appendResponseHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['appendResponseHeaders']
  const assertMethod: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['assertMethod']
  const cachedEventHandler: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/cache')['cachedEventHandler']
  const cachedFunction: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/cache')['cachedFunction']
  const callNodeListener: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['callNodeListener']
  const checkAdminPermission: typeof import('../../utils/permission')['checkAdminPermission']
  const checkCOSFileExists: typeof import('../../utils/cos-uploader')['checkCOSFileExists']
  const cleanupExpiredTokens: typeof import('../../utils/token-blacklist')['cleanupExpiredTokens']
  const cleanupTempFiles: typeof import('../../utils/file-utils')['cleanupTempFiles']
  const clearRefreshTokenCookie: typeof import('../../utils/cookie-utils')['clearRefreshTokenCookie']
  const clearResponseHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['clearResponseHeaders']
  const clearSession: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['clearSession']
  const closePool: typeof import('../../utils/database')['closePool']
  const copyFile: typeof import('../../utils/file-utils')['copyFile']
  const createApp: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['createApp']
  const createAppEventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['createAppEventHandler']
  const createError: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['createError']
  const createEvent: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['createEvent']
  const createEventStream: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['createEventStream']
  const createInitialUserAssets: typeof import('../../utils/userAssets')['createInitialUserAssets']
  const createRouter: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['createRouter']
  const defaultContentType: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defaultContentType']
  const defineCachedEventHandler: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/cache')['defineCachedEventHandler']
  const defineCachedFunction: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/cache')['defineCachedFunction']
  const defineEventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineEventHandler']
  const defineLazyEventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineLazyEventHandler']
  const defineNitroErrorHandler: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/error/utils')['defineNitroErrorHandler']
  const defineNitroPlugin: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/plugin')['defineNitroPlugin']
  const defineNodeListener: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineNodeListener']
  const defineNodeMiddleware: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineNodeMiddleware']
  const defineRenderHandler: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/renderer')['defineRenderHandler']
  const defineRequestMiddleware: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineRequestMiddleware']
  const defineResponseMiddleware: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineResponseMiddleware']
  const defineRouteMeta: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/meta')['defineRouteMeta']
  const defineTask: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/task')['defineTask']
  const defineWebSocket: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineWebSocket']
  const defineWebSocketHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['defineWebSocketHandler']
  const deleteCookie: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['deleteCookie']
  const deleteFile: typeof import('../../utils/file-utils')['deleteFile']
  const deleteFromCOS: typeof import('../../utils/cos-uploader')['deleteFromCOS']
  const dynamicEventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['dynamicEventHandler']
  const ensureDirectoryExists: typeof import('../../utils/file-utils')['ensureDirectoryExists']
  const ensureUserAssets: typeof import('../../utils/userAssets')['ensureUserAssets']
  const eventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['eventHandler']
  const executeTransaction: typeof import('../../utils/database')['executeTransaction']
  const extractAuditInfo: typeof import('../../utils/logger')['extractAuditInfo']
  const fetchWithEvent: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['fetchWithEvent']
  const fileExists: typeof import('../../utils/file-utils')['fileExists']
  const findAdminByCredentials: typeof import('../../utils/auth')['findAdminByCredentials']
  const findAdminById: typeof import('../../utils/auth')['findAdminById']
  const findUserByCredentials: typeof import('../../utils/auth')['findUserByCredentials']
  const findUserById: typeof import('../../utils/auth')['findUserById']
  const forbiddenResponse: typeof import('../../utils/response')['forbiddenResponse']
  const formatFileSize: typeof import('../../utils/file-utils')['formatFileSize']
  const fromNodeMiddleware: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['fromNodeMiddleware']
  const fromPlainHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['fromPlainHandler']
  const fromWebHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['fromWebHandler']
  const generateAccessToken: typeof import('../../utils/auth')['generateAccessToken']
  const generateAdminAccessToken: typeof import('../../utils/auth')['generateAdminAccessToken']
  const generateAdminRefreshToken: typeof import('../../utils/auth')['generateAdminRefreshToken']
  const generateFileHash: typeof import('../../utils/file-utils')['generateFileHash']
  const generatePresignedUrl: typeof import('../../utils/cos-uploader')['generatePresignedUrl']
  const generateRefreshToken: typeof import('../../utils/auth')['generateRefreshToken']
  const generateUniqueFilename: typeof import('../../utils/file-utils')['generateUniqueFilename']
  const generateUserAccessToken: typeof import('../../utils/auth')['generateUserAccessToken']
  const generateUserRefreshToken: typeof import('../../utils/auth')['generateUserRefreshToken']
  const getAdminPermissionCodes: typeof import('../../utils/permission')['getAdminPermissionCodes']
  const getAdminPermissions: typeof import('../../utils/auth')['getAdminPermissions']
  const getBlacklistStats: typeof import('../../utils/token-blacklist')['getBlacklistStats']
  const getCOSFileInfo: typeof import('../../utils/cos-uploader')['getCOSFileInfo']
  const getClientIP: typeof import('../../utils/logger')['getClientIP']
  const getCookie: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getCookie']
  const getFileExtension: typeof import('../../utils/file-utils')['getFileExtension']
  const getFileSize: typeof import('../../utils/file-utils')['getFileSize']
  const getHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getHeader']
  const getHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getHeaders']
  const getMethod: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getMethod']
  const getPool: typeof import('../../utils/database')['getPool']
  const getProxyRequestHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getProxyRequestHeaders']
  const getQuery: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getQuery']
  const getRefreshTokenFromCookie: typeof import('../../utils/cookie-utils')['getRefreshTokenFromCookie']
  const getRequestFingerprint: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestFingerprint']
  const getRequestHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestHeader']
  const getRequestHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestHeaders']
  const getRequestHost: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestHost']
  const getRequestIP: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestIP']
  const getRequestPath: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestPath']
  const getRequestProtocol: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestProtocol']
  const getRequestURL: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestURL']
  const getRequestWebStream: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRequestWebStream']
  const getResponseHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getResponseHeader']
  const getResponseHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getResponseHeaders']
  const getResponseStatus: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getResponseStatus']
  const getResponseStatusText: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getResponseStatusText']
  const getRouteRules: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/route-rules')['getRouteRules']
  const getRouterParam: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRouterParam']
  const getRouterParams: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getRouterParams']
  const getSession: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getSession']
  const getUploadPath: typeof import('../../utils/file-utils')['getUploadPath']
  const getUserAssetBalance: typeof import('../../utils/userAssets')['getUserAssetBalance']
  const getValidatedQuery: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getValidatedQuery']
  const getValidatedRouterParams: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['getValidatedRouterParams']
  const handleCacheHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['handleCacheHeaders']
  const handleCors: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['handleCors']
  const hasAdminPermission: typeof import('../../utils/auth')['hasAdminPermission']
  const hasPermission: typeof import('../../utils/auth')['hasPermission']
  const hashPassword: typeof import('../../utils/auth')['hashPassword']
  const initJWTConfig: typeof import('../../utils/auth')['initJWTConfig']
  const invalidateAllAdminTokens: typeof import('../../utils/token-blacklist')['invalidateAllAdminTokens']
  const invalidateAllUserTokens: typeof import('../../utils/token-blacklist')['invalidateAllUserTokens']
  const isAdmin: typeof import('../../utils/auth')['isAdmin']
  const isAdminToken: typeof import('../../utils/auth')['isAdminToken']
  const isCorsOriginAllowed: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isCorsOriginAllowed']
  const isError: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isError']
  const isEvent: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isEvent']
  const isEventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isEventHandler']
  const isMethod: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isMethod']
  const isObjectStorageAvailable: typeof import('../../utils/cos-http-uploader')['isObjectStorageAvailable']
  const isPreflightRequest: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isPreflightRequest']
  const isStream: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isStream']
  const isSuperAdmin: typeof import('../../utils/permission')['isSuperAdmin']
  const isTokenBlacklisted: typeof import('../../utils/token-blacklist')['isTokenBlacklisted']
  const isUserToken: typeof import('../../utils/auth')['isUserToken']
  const isWebResponse: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['isWebResponse']
  const lazyEventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['lazyEventHandler']
  const logAdminAction: typeof import('../../utils/logger')['logAdminAction']
  const logAuditAction: typeof import('../../utils/logger')['logAuditAction']
  const logger: typeof import('../../utils/logger')['logger']
  const moveFile: typeof import('../../utils/file-utils')['moveFile']
  const nitroPlugin: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/plugin')['nitroPlugin']
  const paginatedQuery: typeof import('../../utils/database')['paginatedQuery']
  const pagination: typeof import('../../utils/response')['pagination']
  const parseCookies: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['parseCookies']
  const promisifyNodeListener: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['promisifyNodeListener']
  const proxyRequest: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['proxyRequest']
  const query: typeof import('../../utils/database')['query']
  const queryInTransaction: typeof import('../../utils/database')['queryInTransaction']
  const readBody: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['readBody']
  const readFormData: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['readFormData']
  const readMultipartFormData: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['readMultipartFormData']
  const readRawBody: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['readRawBody']
  const readValidatedBody: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['readValidatedBody']
  const removeResponseHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['removeResponseHeader']
  const requirePermission: typeof import('../../utils/permission')['requirePermission']
  const runTask: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/task')['runTask']
  const sanitizeStatusCode: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sanitizeStatusCode']
  const sanitizeStatusMessage: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sanitizeStatusMessage']
  const sealSession: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sealSession']
  const send: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['send']
  const sendError: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sendError']
  const sendIterable: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sendIterable']
  const sendNoContent: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sendNoContent']
  const sendProxy: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sendProxy']
  const sendRedirect: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sendRedirect']
  const sendStream: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sendStream']
  const sendWebResponse: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['sendWebResponse']
  const serveStatic: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['serveStatic']
  const serverErrorResponse: typeof import('../../utils/response')['serverErrorResponse']
  const setCookie: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['setCookie']
  const setHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['setHeader']
  const setHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['setHeaders']
  const setRefreshTokenCookie: typeof import('../../utils/cookie-utils')['setRefreshTokenCookie']
  const setResponseHeader: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['setResponseHeader']
  const setResponseHeaders: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['setResponseHeaders']
  const setResponseStatus: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['setResponseStatus']
  const sleep: typeof import('../../utils/response')['sleep']
  const splitCookiesString: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['splitCookiesString']
  const testConnection: typeof import('../../utils/database')['testConnection']
  const toEventHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['toEventHandler']
  const toNodeListener: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['toNodeListener']
  const toPlainHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['toPlainHandler']
  const toWebHandler: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['toWebHandler']
  const toWebRequest: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['toWebRequest']
  const transaction: typeof import('../../utils/database')['transaction']
  const unAuthorizedResponse: typeof import('../../utils/response')['unAuthorizedResponse']
  const unsealSession: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['unsealSession']
  const updateAdminLastLogin: typeof import('../../utils/auth')['updateAdminLastLogin']
  const updateLastLogin: typeof import('../../utils/auth')['updateLastLogin']
  const updateSession: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['updateSession']
  const updateUserLastLogin: typeof import('../../utils/auth')['updateUserLastLogin']
  const uploadToCOS: typeof import('../../utils/cos-uploader')['uploadToCOS']
  const uploadToObjectStorage: typeof import('../../utils/cos-http-uploader')['uploadToObjectStorage']
  const useAppConfig: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/config')['useAppConfig']
  const useBase: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['useBase']
  const useEvent: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/context')['useEvent']
  const useNitroApp: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/app')['useNitroApp']
  const usePageResponseSuccess: typeof import('../../utils/response')['usePageResponseSuccess']
  const useResponseError: typeof import('../../utils/response')['useResponseError']
  const useResponseSuccess: typeof import('../../utils/response')['useResponseSuccess']
  const useRuntimeConfig: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/config')['useRuntimeConfig']
  const useSession: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['useSession']
  const useStorage: typeof import('../../node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/runtime/internal/storage')['useStorage']
  const useVbenError: typeof import('../../utils/response')['useVbenError']
  const useVbenSuccess: typeof import('../../utils/response')['useVbenSuccess']
  const validateEmail: typeof import('../../utils/validators')['validateEmail']
  const validateFileType: typeof import('../../utils/file-utils')['validateFileType']
  const validateFundData: typeof import('../../utils/validators')['validateFundData']
  const validateId: typeof import('../../utils/validators')['validateId']
  const validatePagination: typeof import('../../utils/validators')['validatePagination']
  const validatePassword: typeof import('../../utils/validators')['validatePassword']
  const validatePhone: typeof import('../../utils/validators')['validatePhone']
  const validateStatus: typeof import('../../utils/validators')['validateStatus']
  const validateUsername: typeof import('../../utils/validators')['validateUsername']
  const validationErrorResponse: typeof import('../../utils/response')['validationErrorResponse']
  const verifyAccessToken: typeof import('../../utils/auth')['verifyAccessToken']
  const verifyAdminAccessToken: typeof import('../../utils/auth')['verifyAdminAccessToken']
  const verifyAdminToken: typeof import('../../utils/auth')['verifyAdminToken']
  const verifyPassword: typeof import('../../utils/auth')['verifyPassword']
  const verifyRefreshToken: typeof import('../../utils/auth')['verifyRefreshToken']
  const verifyToken: typeof import('../../utils/auth')['verifyToken']
  const verifyUserAccessToken: typeof import('../../utils/auth')['verifyUserAccessToken']
  const wasAdminTokenIssuedBeforeGlobalLogout: typeof import('../../utils/token-blacklist')['wasAdminTokenIssuedBeforeGlobalLogout']
  const wasTokenIssuedBeforeGlobalLogout: typeof import('../../utils/token-blacklist')['wasTokenIssuedBeforeGlobalLogout']
  const writeEarlyHints: typeof import('../../node_modules/.pnpm/h3@1.15.3/node_modules/h3')['writeEarlyHints']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { UserInfo, AdminInfo, UserJWTPayload, AdminJWTPayload } from '../../utils/auth'
  import('../../utils/auth')
  // @ts-ignore
  export type { PaginationParams, PaginationResult, Pool, PoolConnection, ResultSetHeader, RowDataPacket } from '../../utils/database'
  import('../../utils/database')
  // @ts-ignore
  export type { LogLevel, AuditLogData } from '../../utils/logger'
  import('../../utils/logger')
}
export { useNitroApp } from 'nitropack/runtime/internal/app';
export { useRuntimeConfig, useAppConfig } from 'nitropack/runtime/internal/config';
export { defineNitroPlugin, nitroPlugin } from 'nitropack/runtime/internal/plugin';
export { defineCachedFunction, defineCachedEventHandler, cachedFunction, cachedEventHandler } from 'nitropack/runtime/internal/cache';
export { useStorage } from 'nitropack/runtime/internal/storage';
export { defineRenderHandler } from 'nitropack/runtime/internal/renderer';
export { defineRouteMeta } from 'nitropack/runtime/internal/meta';
export { getRouteRules } from 'nitropack/runtime/internal/route-rules';
export { useEvent } from 'nitropack/runtime/internal/context';
export { defineTask, runTask } from 'nitropack/runtime/internal/task';
export { defineNitroErrorHandler } from 'nitropack/runtime/internal/error/utils';
export { appendCorsHeaders, appendCorsPreflightHeaders, appendHeader, appendHeaders, appendResponseHeader, appendResponseHeaders, assertMethod, callNodeListener, clearResponseHeaders, clearSession, createApp, createAppEventHandler, createError, createEvent, createEventStream, createRouter, defaultContentType, defineEventHandler, defineLazyEventHandler, defineNodeListener, defineNodeMiddleware, defineRequestMiddleware, defineResponseMiddleware, defineWebSocket, defineWebSocketHandler, deleteCookie, dynamicEventHandler, eventHandler, fetchWithEvent, fromNodeMiddleware, fromPlainHandler, fromWebHandler, getCookie, getHeader, getHeaders, getMethod, getProxyRequestHeaders, getQuery, getRequestFingerprint, getRequestHeader, getRequestHeaders, getRequestHost, getRequestIP, getRequestPath, getRequestProtocol, getRequestURL, getRequestWebStream, getResponseHeader, getResponseHeaders, getResponseStatus, getResponseStatusText, getRouterParam, getRouterParams, getSession, getValidatedQuery, getValidatedRouterParams, handleCacheHeaders, handleCors, isCorsOriginAllowed, isError, isEvent, isEventHandler, isMethod, isPreflightRequest, isStream, isWebResponse, lazyEventHandler, parseCookies, promisifyNodeListener, proxyRequest, readBody, readFormData, readMultipartFormData, readRawBody, readValidatedBody, removeResponseHeader, sanitizeStatusCode, sanitizeStatusMessage, sealSession, send, sendError, sendIterable, sendNoContent, sendProxy, sendRedirect, sendStream, sendWebResponse, serveStatic, setCookie, setHeader, setHeaders, setResponseHeader, setResponseHeaders, setResponseStatus, splitCookiesString, toEventHandler, toNodeListener, toPlainHandler, toWebHandler, toWebRequest, unsealSession, updateSession, useBase, useSession, writeEarlyHints } from 'h3';
export { initJWTConfig, generateUserAccessToken, generateAdminAccessToken, generateUserRefreshToken, generateAdminRefreshToken, verifyUserAccessToken, verifyAdminAccessToken, verifyRefreshToken, verifyToken, hashPassword, verifyPassword, findUserByCredentials, findAdminByCredentials, findUserById, findAdminById, updateUserLastLogin, updateAdminLastLogin, isAdminToken, isUserToken, hasAdminPermission, getAdminPermissions, generateAccessToken, generateRefreshToken, verifyAccessToken, updateLastLogin, isAdmin, hasPermission, verifyAdminToken } from 'D:/ProjectV/ReelShortFund/backend/utils/auth';
export { clearRefreshTokenCookie, setRefreshTokenCookie, getRefreshTokenFromCookie } from 'D:/ProjectV/ReelShortFund/backend/utils/cookie-utils';
export { uploadToObjectStorage, isObjectStorageAvailable } from 'D:/ProjectV/ReelShortFund/backend/utils/cos-http-uploader';
export { uploadToCOS, deleteFromCOS, checkCOSFileExists, getCOSFileInfo, generatePresignedUrl } from 'D:/ProjectV/ReelShortFund/backend/utils/cos-uploader';
export { getPool, testConnection, query, executeTransaction, queryInTransaction, transaction, paginatedQuery, closePool } from 'D:/ProjectV/ReelShortFund/backend/utils/database';
export { generateUniqueFilename, ensureDirectoryExists, getUploadPath, deleteFile, getFileSize, fileExists, getFileExtension, validateFileType, formatFileSize, generateFileHash, copyFile, moveFile, cleanupTempFiles } from 'D:/ProjectV/ReelShortFund/backend/utils/file-utils';
export { LogLevel, logger, logAuditAction, getClientIP, extractAuditInfo, logAdminAction } from 'D:/ProjectV/ReelShortFund/backend/utils/logger';
export { checkAdminPermission, getAdminPermissionCodes, requirePermission, PERMISSIONS, isSuperAdmin } from 'D:/ProjectV/ReelShortFund/backend/utils/permission';
export { useResponseSuccess, useVbenSuccess, usePageResponseSuccess, useResponseError, useVbenError, forbiddenResponse, unAuthorizedResponse, validationErrorResponse, serverErrorResponse, sleep, pagination } from 'D:/ProjectV/ReelShortFund/backend/utils/response';
export { addTokenToBlacklist, isTokenBlacklisted, wasTokenIssuedBeforeGlobalLogout, wasAdminTokenIssuedBeforeGlobalLogout, cleanupExpiredTokens, invalidateAllUserTokens, invalidateAllAdminTokens, getBlacklistStats } from 'D:/ProjectV/ReelShortFund/backend/utils/token-blacklist';
export { ensureUserAssets, getUserAssetBalance, createInitialUserAssets } from 'D:/ProjectV/ReelShortFund/backend/utils/userAssets';
export { validateEmail, validatePhone, validatePassword, validateUsername, validateFundData, validatePagination, validateId, validateStatus } from 'D:/ProjectV/ReelShortFund/backend/utils/validators';