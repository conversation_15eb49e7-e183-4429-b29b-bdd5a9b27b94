import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './assets/main.css'
import ProtectedRoute from './components/ProtectedRoute.vue'
import { useAuthStore } from './store/auth'
import { updateDocumentMetadata } from './services/settingsService'
import './utils/axiosConfig'
import { ACTIVE_ENVIRONMENT, getCurrentEnvironment, getCompleteApiUrl } from './utils/environmentConfig'
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'

const app = createApp(App)

// 创建并使用Pinia
const pinia = createPinia()
app.use(pinia)

app.use(router)

// 配置Toast
const toastOptions = {
  position: "top-right",
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: "button",
  icon: true,
  rtl: false
}
app.use(Toast, toastOptions)

// 注册路由守卫组件
app.component('ProtectedRoute', ProtectedRoute)

app.mount('#app')

// 初始化认证状态
const authStore = useAuthStore()
authStore.initAuth()

// 初始化系统设置（更新网站标题和图标）
updateDocumentMetadata()

// 输出当前环境信息（仅在开发模式下）
if (import.meta.env.MODE !== 'production') {
  const env = getCurrentEnvironment()
  const apiUrl = getCompleteApiUrl()
  console.log(
    `%c当前环境: ${env.name} (${ACTIVE_ENVIRONMENT}) %c\nAPI地址: ${apiUrl}`,
    'background:#2196f3;color:white;padding:4px;border-radius:3px 3px 0 0;font-weight:bold',
    'background:#e3f2fd;color:#0d47a1;padding:4px;border-radius:0 0 3px 3px'
  )
}
