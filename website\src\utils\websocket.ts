/**
 * WebSocket 客户端工具
 * 用于接收服务器实时通知
 */
import { useAuthStore } from '../stores/auth'
import { useToast } from 'vue-toastification'

export interface WebSocketMessage {
  type: string
  data?: any
  timestamp?: string
}

export class WebSocketClient {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000
  private heartbeatInterval: number | null = null
  private isConnecting = false
  private isManualClose = false

  constructor(
    private token: string,
    private type: 'user' | 'admin' = 'user'
  ) {}

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
        resolve()
        return
      }

      this.isConnecting = true
      this.isManualClose = false

      try {
        // 构建WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = window.location.host
        const wsUrl = `${protocol}//${host}/api/ws?token=${encodeURIComponent(this.token)}&type=${this.type}`

        console.log('正在连接WebSocket...', { type: this.type })

        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('WebSocket连接成功', { type: this.type })
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.startHeartbeat()
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error('WebSocket消息解析失败:', error, event.data)
          }
        }

        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭', { 
            code: event.code, 
            reason: event.reason,
            type: this.type 
          })
          
          this.isConnecting = false
          this.stopHeartbeat()

          // 如果不是手动关闭且连接异常，尝试重连
          if (!this.isManualClose && event.code !== 1000) {
            this.attemptReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.isConnecting = false
          
          if (this.reconnectAttempts === 0) {
            reject(error)
          }
        }

      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage) {
    console.log('收到WebSocket消息:', message)

    switch (message.type) {
      case 'CONNECTION_SUCCESS':
        console.log('WebSocket连接确认:', message.data)
        break

      case 'FORCE_LOGOUT':
        this.handleForceLogout(message.data)
        break

      case 'PONG':
        // 心跳响应，无需处理
        break

      case 'ONLINE_STATS':
        // 在线统计，可以触发事件供组件监听
        this.emitEvent('onlineStats', message.data)
        break

      default:
        console.log('未处理的WebSocket消息类型:', message.type, message.data)
        break
    }
  }

  /**
   * 处理强制登出
   */
  private handleForceLogout(data: any) {
    console.warn('收到强制登出通知:', data)

    // 显示通知消息
    const toast = useToast()
    toast.warning(data?.reason || '您的账户状态已变更，请重新登录')

    // 延迟执行登出，确保用户能看到消息
    setTimeout(() => {
      const authStore = useAuthStore()
      authStore.clearAuth()

      // 跳转到登录页
      if (window.location.pathname !== '/login') {
        window.location.href = '/login?reason=force_logout'
      }
    }, 2000)
  }

  /**
   * 发送消息
   */
  send(message: WebSocketMessage): boolean {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('发送WebSocket消息失败:', error)
        return false
      }
    }
    return false
  }

  /**
   * 开始心跳
   */
  private startHeartbeat() {
    this.stopHeartbeat()
    
    this.heartbeatInterval = window.setInterval(() => {
      this.send({
        type: 'PING',
        timestamp: new Date().toISOString()
      })
    }, 30000) // 每30秒发送一次心跳
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * 尝试重连
   */
  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)

    setTimeout(() => {
      if (!this.isManualClose) {
        this.connect().catch(error => {
          console.error('WebSocket重连失败:', error)
        })
      }
    }, this.reconnectInterval * this.reconnectAttempts)
  }

  /**
   * 手动关闭连接
   */
  close() {
    this.isManualClose = true
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close(1000, '用户主动关闭')
      this.ws = null
    }
  }

  /**
   * 获取连接状态
   */
  get readyState(): number {
    return this.ws?.readyState ?? WebSocket.CLOSED
  }

  /**
   * 是否已连接
   */
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  /**
   * 触发自定义事件（简单实现）
   */
  private emitEvent(eventName: string, data: any) {
    const event = new CustomEvent(`websocket:${eventName}`, { detail: data })
    window.dispatchEvent(event)
  }
}

// 全局WebSocket客户端实例
let globalWebSocketClient: WebSocketClient | null = null

/**
 * 初始化WebSocket连接
 */
export async function initWebSocket(token: string, type: 'user' | 'admin' = 'user'): Promise<WebSocketClient> {
  // 如果已有连接且token相同，直接返回
  if (globalWebSocketClient && globalWebSocketClient.isConnected) {
    return globalWebSocketClient
  }

  // 关闭旧连接
  if (globalWebSocketClient) {
    globalWebSocketClient.close()
  }

  // 创建新连接
  globalWebSocketClient = new WebSocketClient(token, type)
  
  try {
    await globalWebSocketClient.connect()
    console.log('WebSocket初始化成功')
    return globalWebSocketClient
  } catch (error) {
    console.error('WebSocket初始化失败:', error)
    throw error
  }
}

/**
 * 关闭WebSocket连接
 */
export function closeWebSocket() {
  if (globalWebSocketClient) {
    globalWebSocketClient.close()
    globalWebSocketClient = null
  }
}

/**
 * 获取当前WebSocket客户端
 */
export function getWebSocketClient(): WebSocketClient | null {
  return globalWebSocketClient
}

/**
 * 发送WebSocket消息
 */
export function sendWebSocketMessage(message: WebSocketMessage): boolean {
  return globalWebSocketClient?.send(message) ?? false
}
