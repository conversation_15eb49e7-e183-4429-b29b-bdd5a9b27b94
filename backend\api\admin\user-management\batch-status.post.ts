import { query as dbQuery } from '~/utils/database';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';
import { logger, logAuditAction, getClientIP } from '~/utils/logger';
import { kickOutUser } from '~/utils/websocket';

/**
 * 管理员批量修改用户状态接口
 * POST /api/admin/user-management/batch-status
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法修改用户状态'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { ids, status } = body;

    // 验证参数
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '请提供要修改的用户ID列表'
      });
    }

    if (status !== 'active' && status !== 'inactive') {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的状态值，只能是 active 或 inactive'
      });
    }

    // 验证ID格式
    const validIds = ids.filter(id => Number.isInteger(id) && id > 0);
    if (validIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的用户ID列表'
      });
    }

    // 查询要修改的用户信息
    const placeholders = validIds.map(() => '?').join(',');
    const users = await dbQuery(
      `SELECT id, username, status FROM users WHERE id IN (${placeholders})`,
      validIds
    ) as any[];

    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '未找到要修改的用户'
      });
    }

    const statusValue = status === 'active' ? 1 : 0;

    // 执行批量状态更新
    await dbQuery(
      `UPDATE users SET status = ?, updated_at = NOW() WHERE id IN (${placeholders})`,
      [statusValue, ...validIds]
    );

    // 如果是批量封禁用户，通过WebSocket踢出所有被封禁的用户
    if (status === 'inactive') {
      let kickedCount = 0;
      for (const user of users) {
        const kickSuccess = kickOutUser(user.id, '您的账户已被管理员禁用，请联系客服了解详情');
        if (kickSuccess) {
          kickedCount++;
        }
      }

      logger.info('批量用户封禁WebSocket通知', {
        totalUsers: users.length,
        kickedCount,
        adminId: admin.id,
        adminUsername: admin.username,
        usernames: users.map(u => u.username)
      });
    }

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_BATCH_UPDATE_USER_STATUS',
      description: `管理员批量修改用户状态: ${users.map(u => u.username).join(', ')} -> ${status}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: { 
        targetUserIds: validIds,
        targetUsers: users.map(u => ({
          id: u.id,
          username: u.username,
          oldStatus: u.status === 1 ? 'active' : 'inactive'
        })),
        newStatus: status
      }
    });

    logger.info('管理员批量修改用户状态成功', {
      adminId: admin.id,
      targetUserIds: validIds,
      updatedCount: users.length,
      newStatus: status,
      ip: getClientIP(event)
    });

    return {
      success: true,
      message: `成功修改 ${users.length} 个用户状态为${status === 'active' ? '启用' : '禁用'}`,
      data: {
        updatedCount: users.length,
        updatedUsers: users.map(u => u.username),
        newStatus: status
      }
    };

  } catch (error) {
    logger.error('批量修改用户状态失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });
    throw error;
  }
});
