"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;
var _dotenv = await jitiImport("dotenv");
var _path = await jitiImport("path");






var _config = await jitiImport("nitropack/config");
var _error = _interopRequireDefault(await jitiImport("./error"));function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };} // 首先加载环境变量
// 明确加载 backend/.env 文件（使用相对路径）
const envPath = (0, _path.resolve)(__dirname, '.env');console.log('加载环境变量文件:', envPath);(0, _dotenv.config)({ path: envPath, override: true });process.env.COMPATIBILITY_DATE = new Date().toISOString();

// 验证必需的环境变量
console.log('调试: 环境变量加载情况:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');
console.log('DB_NAME:', process.env.DB_NAME);

if (!process.env.DB_HOST) {
  console.error('错误: DB_HOST 环境变量未设置');
  process.exit(1);
}
if (!process.env.DB_USER) {
  console.error('错误: DB_USER 环境变量未设置');
  process.exit(1);
}var _default = exports.default =

(0, _config.defineNitroConfig)({
  devErrorHandler: _error.default,
  errorHandler: '~/error',
  experimental: {
    wasm: true,
    websocket: true
  },
  typescript: {
    generateTsConfig: false
  },
  runtimeConfig: {
    // 数据库配置
    dbHost: process.env.DB_HOST,
    dbPort: process.env.DB_PORT,
    dbUser: process.env.DB_USER,
    dbPassword: process.env.DB_PASSWORD,
    dbName: process.env.DB_NAME,

    // JWT配置
    jwtSecret: process.env.JWT_SECRET,
    jwtExpiresIn: process.env.JWT_EXPIRES_IN,
    jwtAdminExpiresIn: process.env.JWT_ADMIN_EXPIRES_IN,
    refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET,

    // 文件上传配置
    uploadDir: process.env.UPLOAD_DIR,
    maxFileSize: process.env.MAX_FILE_SIZE,
    allowedFileTypes: process.env.ALLOWED_FILE_TYPES,

    // 应用配置
    appUrl: process.env.APP_URL,
    apiUrl: process.env.API_URL,

    // 邮件配置
    smtpHost: process.env.SMTP_HOST,
    smtpPort: process.env.SMTP_PORT,
    smtpUser: process.env.SMTP_USER,
    smtpPass: process.env.SMTP_PASS,
    smtpFrom: process.env.SMTP_FROM,

    // 腾讯云COS配置
    cosSecretId: process.env.COS_SECRET_ID,
    cosSecretKey: process.env.COS_SECRET_KEY,
    cosRegion: process.env.COS_REGION,
    cosBucket: process.env.COS_BUCKET,
    cosDirectory: process.env.COS_DIRECTORY,

    // 阿里云OSS配置
    ossProvider: process.env.OSS_PROVIDER,
    aliOssRegion: process.env.ALI_OSS_REGION,
    aliOssAccessKeyId: process.env.ALI_OSS_ACCESS_KEY_ID,
    aliOssAccessKeySecret: process.env.ALI_OSS_ACCESS_KEY_SECRET,
    aliOssBucket: process.env.ALI_OSS_BUCKET,

    // 日志配置
    logLevel: process.env.LOG_LEVEL
  },
  routeRules: {
    '/api/**': {
      cors: true,
      headers: {
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Headers':
        'Accept, Authorization, Content-Length, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-CSRF-TOKEN, X-Requested-With',
        'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Expose-Headers': '*'
      }
    }
  },
  devServer: {
    watch: []
  }
}); /* v9-54a3c0ffda4c6861 */
