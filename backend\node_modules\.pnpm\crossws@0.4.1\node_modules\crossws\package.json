{"name": "crossws", "version": "0.4.1", "description": "Cross-platform WebSocket Servers for Node.js, Deno, Bun and Cloudflare Workers", "homepage": "https://crossws.h3.dev", "repository": "h3js/crossws", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": "./dist/index.mjs", "./adapters/bun": "./dist/adapters/bun.mjs", "./adapters/deno": "./dist/adapters/deno.mjs", "./adapters/cloudflare": "./dist/adapters/cloudflare.mjs", "./adapters/sse": "./dist/adapters/sse.mjs", "./adapters/node": "./dist/adapters/node.mjs", "./adapters/uws": "./dist/adapters/uws.mjs", "./server/bun": "./dist/server/bun.mjs", "./server/deno": "./dist/server/deno.mjs", "./server/node": "./dist/server/node.mjs", "./server": {"deno": "./dist/server/deno.mjs", "bun": "./dist/server/bun.mjs", "workerd": "./dist/server/cloudflare.mjs", "node": "./dist/server/node.mjs", "default": "./dist/server/default.mjs"}, "./websocket/sse": "./dist/websocket/sse.mjs", "./websocket": {"browser": "./dist/websocket/native.mjs", "worker": "./dist/websocket/native.mjs", "bun": "./dist/websocket/native.mjs", "deno": "./dist/websocket/native.mjs", "edge-light": "./dist/websocket/native.mjs", "workerd": "./dist/websocket/native.mjs", "node": "./dist/websocket/node.mjs", "default": "./dist/websocket/native.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist", "adapters", "websocket", "server", "*.d.ts"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint --cache . && prettier -c src test", "lint:fix": "eslint --cache . --fix && prettier -w src test", "prepack": "pnpm run build", "play:bun": "bun --watch test/fixture/bun.ts", "play:cf": "wrangler dev --port 3001 -c test/fixture/wrangler.toml", "play:cf-durable": "wrangler dev --port 3001 -c test/fixture/wrangler-durable.toml", "play:deno": "deno run --watch --unstable-byonm -A test/fixture/deno.ts", "play:node": "jiti test/fixture/node.ts", "play:sse": "deno run --unstable-byonm -A --watch test/fixture/sse.ts", "play:uws": "jiti test/fixture/uws.ts", "release": "pnpm test && pnpm build && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "resolutions": {"crossws": "workspace:*"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250523.0", "@types/bun": "^1.2.14", "@types/deno": "^2.3.0", "@types/node": "^22.15.21", "@types/web": "^0.0.235", "@types/ws": "^8.18.1", "@vitest/coverage-v8": "^3.1.4", "automd": "^0.4.0", "changelogen": "^0.6.1", "consola": "^3.4.2", "eslint": "^9.27.0", "eslint-config-unjs": "^0.4.2", "eventsource": "^4.0.0", "execa": "^9.5.3", "get-port-please": "^3.1.2", "h3": "^1.15.3", "jiti": "^2.4.2", "listhen": "^1.9.0", "prettier": "^3.5.3", "srvx": "^0.7.1", "typescript": "^5.8.3", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.44.0", "unbuild": "^3.5.0", "undici": "^7.10.0", "vitest": "^3.1.4", "wrangler": "^4.16.1", "ws": "^8.18.2"}, "peerDependencies": {"srvx": ">=0.7.1"}, "peerDependenciesMeta": {"srvx": {"optional": true}}, "packageManager": "pnpm@10.11.0", "pnpm": {"ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "sharp", "workerd"]}}