/**
 * 网站资源上传接口（Logo、图标等）
 * POST /api/admin/upload/site-asset
 * 专门用于系统设置中的Logo和图标上传
 */
import { writeFile } from 'fs/promises';

export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法上传网站资源'
      });
    }

    // 检查对象存储是否可用
    const isStorageAvailable = await isObjectStorageAvailable();
    if (!isStorageAvailable) {
      throw createError({
        statusCode: 503,
        statusMessage: '对象存储服务不可用，请检查系统设置'
      });
    }

    // 获取上传的文件
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '未选择文件'
      });
    }

    const fileData = formData.find(item => item.name === 'file');
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: '文件数据无效'
      });
    }

    // 获取资源类型参数（logo、favicon等）
    const assetTypeData = formData.find(item => item.name === 'assetType');
    const assetType = assetTypeData?.data ? new TextDecoder().decode(assetTypeData.data) : 'logo';

    // 验证资源类型
    const validAssetTypes = ['logo', 'favicon', 'icon', 'qrcode'];
    if (!validAssetTypes.includes(assetType)) {
      throw createError({
        statusCode: 400,
        statusMessage: `不支持的资源类型: ${assetType}。支持的类型: ${validAssetTypes.join(', ')}`
      });
    }

    // 根据资源类型设置允许的文件类型
    let allowedTypes: string[];
    switch (assetType) {
      case 'favicon':
        allowedTypes = ['.ico', '.png', '.svg'];
        break;
      case 'icon':
        allowedTypes = ['.png', '.svg', '.jpg', '.jpeg'];
        break;
      case 'qrcode':
        allowedTypes = ['.png', '.jpg', '.jpeg'];
        break;
      case 'logo':
      default:
        allowedTypes = ['.png', '.svg', '.jpg', '.jpeg', '.gif'];
        break;
    }

    // 验证文件类型
    const fileExt = getFileExtension(fileData.filename);
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `${assetType}不支持的文件类型: ${fileExt}。支持的类型: ${allowedTypes.join(', ')}`
      });
    }

    // 验证文件大小（网站资源限制为2MB）
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `文件大小超过限制，最大允许 ${Math.round(maxSize / 1024 / 1024)}MB`
      });
    }

    // 生成唯一文件名
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    
    // 保存临时文件
    const tempPath = await getUploadPath(uniqueFilename, 'temp');
    await writeFile(tempPath, fileData.data);

    logger.info('开始上传网站资源', {
      assetType,
      originalName: fileData.filename,
      fileName: uniqueFilename,
      fileSize: fileData.data.length,
      fileType: fileExt,
      adminId: admin.id
    });

    try {
      // 构建目标路径
      const destPath = `site-assets/${assetType}/${uniqueFilename}`;

      logger.info('网站资源上传路径信息', {
        assetType,
        uniqueFilename,
        destPath,
        originalName: fileData.filename
      });

      // 上传到对象存储
      const uploadResult = await uploadToObjectStorage({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || 'image/png'
      }, destPath);

      // 删除临时文件
      await deleteFile(tempPath);

      // 记录审计日志
      await logAuditAction({
        action: 'ADMIN_UPLOAD_SITE_ASSET',
        description: `管理员上传网站${assetType}: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, 'user-agent') || '',
        details: {
          assetType,
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          fileType: fileExt,
          url: uploadResult.url,
          provider: uploadResult.provider
        }
      });

      logger.info('网站资源上传成功', {
        assetType,
        originalName: fileData.filename,
        url: uploadResult.url,
        provider: uploadResult.provider,
        adminId: admin.id
      });

      return {
        success: true,
        data: {
          url: uploadResult.url,
          filename: uniqueFilename,
          originalname: fileData.filename,
          mimetype: fileData.type || 'image/png',
          size: fileData.data.length,
          assetType,
          provider: uploadResult.provider,
          key: uploadResult.key
        }
      };

    } catch (uploadError: any) {
      // 删除临时文件
      await deleteFile(tempPath);
      
      logger.error('网站资源上传失败', {
        error: uploadError.message,
        assetType,
        originalName: fileData.filename,
        adminId: admin.id
      });

      throw createError({
        statusCode: 500,
        statusMessage: `网站资源上传失败: ${uploadError.message}`
      });
    }

  } catch (error: any) {
    logger.error('网站资源上传接口错误', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '网站资源上传失败，请稍后重试'
    });
  }
});
