/**
 * 获取用户充值记录接口
 * GET /api/users/wallet/recharge/records
 */

import { query } from '~/utils/database'
import { verifyUserAccessToken } from '~/utils/auth'

export default defineEventHandler(async (event) => {
  try {
    console.log('Recharge records API called');

    // 验证用户认证
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户认证失败，请重新登录'
      });
    }

    const userId = userPayload.id;

    // 获取查询参数
    const queryParams = getQuery(event);
    const page = parseInt(queryParams.page as string) || 1;
    const pageSize = parseInt(queryParams.pageSize as string) || 10;
    const offset = (page - 1) * pageSize;

    // 查询充值记录总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
    `;
    const countResult = await query(countQuery, [userId]);
    const total = countResult[0].total;

    // 查询充值记录详情
    const recordsQuery = `
      SELECT
        id,
        amount,
        balance_before,
        balance_after,
        transaction_no,
        description,
        status,
        DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_at,
        DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i:%s') as updated_at
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const recordsRows = await query(recordsQuery, [userId, pageSize, offset]);

    // 处理充值记录数据
    const records = recordsRows.map(record => ({
      id: record.id,
      transactionNo: record.transaction_no, // 只显示TXN开头的交易号
      amount: parseFloat(record.amount),
      balanceBefore: parseFloat(record.balance_before),
      balanceAfter: parseFloat(record.balance_after),
      description: record.description,
      status: record.status,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
      completedAt: record.status === 'completed' ? record.updated_at : null,
      // 模拟支付方式（实际应该从订单表或支付记录表获取）
      paymentMethod: getRandomPaymentMethod(),
      failReason: record.status === 'failed' ? '支付超时' : null
    }));

    return {
      success: true,
      data: {
        records,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };

  } catch (error: any) {
    console.error('获取充值记录失败:', error);
    return {
      success: false,
      message: '获取充值记录失败',
      error: error.message
    };
  }
});

// 随机生成支付方式（临时方案）
function getRandomPaymentMethod() {
  const methods = ['alipay', 'wechat', 'bank'];
  return methods[Math.floor(Math.random() * methods.length)];
}
