/**
 * 获取安全设置接口
 * GET /api/admin/settings/security
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法访问安全设置'
      });
    }

    // 从数据库获取安全设置
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['security_settings']
    );

    let securitySettings = {};
    
    if (result.length > 0 && result[0].setting_value) {
      try {
        securitySettings = JSON.parse(result[0].setting_value);
        // JWT密钥等敏感字段明文显示，因为这些是网站配置项
      } catch (error) {
        logger.error('解析安全设置JSON失败', { error: error.message });
      }
    }

    // 如果没有设置，返回默认值（与.env文件字段对齐）
    if (Object.keys(securitySettings).length === 0) {
      securitySettings = {
        jwtSecret: '',
        jwtExpiresIn: '24h',
        jwtAdminExpiresIn: '12h',
        accessTokenSecret: '',
        refreshTokenSecret: '',
        passwordMinLength: 8,
        enableCaptcha: true,
        maxLoginAttempts: 5,
        lockTime: 30
      };
    }

    return {
      success: true,
      data: securitySettings
    };

  } catch (error: any) {
    logger.error('获取安全设置失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
