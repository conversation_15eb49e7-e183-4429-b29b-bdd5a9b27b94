<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../store/auth'
import { getDashboardData } from '../api/dashboard'
import api from '../api/request'
import { requireLogin, handleAuthError, confirmLogout } from '../utils/authUtils'

import UserAvatar from '../components/common/UserAvatar.vue'
import SimpleRechargeDialog from '../components/dashboard/SimpleRechargeDialog.vue'
import GridCard from '../components/dashboard/GridCard.vue'
import { formatNumber, formatPercentage, getStatusColorClass } from '../utils/format'
import type { DashboardData, AssetOverview, UserProject, ReturnTrendPoint, InvestmentDistribution } from '../types'
import Swal from 'sweetalert2'

const router = useRouter()
const authStore = useAuthStore()

// 仪表板数据
const dashboardData = ref<DashboardData | null>(null)
const loading = ref<boolean>(true)

// 充值弹窗状态
const showRechargeDialog = ref<boolean>(false)



// 用户信息
const userInfo = computed(() => {
  if (authStore.user) {
    return {
      name: authStore.user.username || '投资者',
      level: getUserLevel(dashboardData.value?.assetOverview?.totalShells || 0),
      lastLogin: new Date().toLocaleString(),
      avatar: authStore.user.avatar || '/images/default-avatar.png'
    }
  }

  return {
    name: '投资者',
    level: '普通会员',
    lastLogin: new Date().toLocaleString(),
    avatar: '/images/default-avatar.png'
  }
})

// 根据投资金额确定用户等级
const getUserLevel = (totalShells: number): string => {
  if (totalShells >= 1000000) return '钻石投资人'
  if (totalShells >= 500000) return '白金投资人'
  if (totalShells >= 100000) return '金牌投资人'
  if (totalShells >= 50000) return '银牌投资人'
  return '普通投资人'
}

// 资产概览数据
const assetOverview = computed(() => {
  return dashboardData.value?.assetOverview || {
    totalShells: 0,
    consumedShells: 0,
    totalDiamonds: 0,
    returnRate: 0,
    availableShells: 0,
    frozenShells: 0,
    projectsCount: 0,
    monthlyDiamonds: 0
  }
})

// 获取仪表板数据
const fetchDashboardData = async () => {
  try {
    loading.value = true
    const response = await getDashboardData()
    if (response.success) {
      dashboardData.value = response.data
    }
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    Swal.fire({
      title: '数据加载失败',
      text: '请刷新页面重试',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 实现退出登录功能
const logout = async () => {
  await confirmLogout(authStore)
}

// 判断用户是否为管理员
const isAdmin = computed(() => {
  // 这里需要根据实际的用户数据结构来判断
  // 暂时返回false，可以根据需要调整
  return false
})

// 打开管理后台
const openAdminDashboard = () => {
  window.open('/admin', '_blank')
}



// 计算收益率：(收益钻石 - 消耗贝壳) / 消耗贝壳
const calculateReturnRate = (): string => {
  const { totalDiamonds, consumedShells } = assetOverview.value
  if (consumedShells === 0) return '0.00'
  return (((totalDiamonds - consumedShells) / consumedShells) * 100).toFixed(2)
}

// 计算所剩投资贝壳：总投资贝壳 - 已消耗贝壳
const calculateRemainingShells = (): number => {
  const { totalShells, consumedShells } = assetOverview.value
  return totalShells - consumedShells
}

// 打开充值弹窗
const openRechargeDialog = () => {
  showRechargeDialog.value = true
}

// 关闭充值弹窗
const closeRechargeDialog = () => {
  showRechargeDialog.value = false
}

// 查看投资记录
const showInvestmentRecords = () => {
  router.push('/investment-records')
}

// 查看充值记录
const showRechargeRecords = () => {
  router.push('/recharge-records')
}

// 充值成功回调
const onRechargeSuccess = (data: any) => {
  // 刷新仪表板数据
  fetchDashboardData()

  // 显示成功提示
  Swal.fire({
    title: '充值成功！',
    text: `成功充值 ${data.amount.toLocaleString()} 贝壳`,
    icon: 'success',
    timer: 2000,
    showConfirmButton: false
  })
}

// 刷新资产数据
const refreshAssetData = async () => {
  try {
    // 检查登录状态
    if (!requireLogin(router)) {
      return
    }

    // 使用带认证的API调用
    const response = await api.get('/users/wallet/balance')

    if (response.success) {
      // 更新资产概览数据
      if (dashboardData.value?.assetOverview) {
        dashboardData.value.assetOverview.totalShells = response.data.totalInvestedShells
        dashboardData.value.assetOverview.availableShells = response.data.shellsBalance
        dashboardData.value.assetOverview.totalDiamonds = response.data.totalEarnedDiamonds
        dashboardData.value.assetOverview.frozenShells = response.data.frozenShells
      }

      // 显示刷新成功提示
      Swal.fire({
        title: '刷新成功',
        text: '资产数据已更新',
        icon: 'success',
        timer: 1500,
        showConfirmButton: false
      })
    }
  } catch (error) {
    console.error('刷新资产数据失败:', error)
    handleAuthError(error, router)

    Swal.fire({
      title: '刷新失败',
      text: '获取最新资产数据失败，请稍后再试',
      icon: 'error'
    })
  }
}

// 用户项目数据
const userProjects = computed(() => {
  return dashboardData.value?.userProjects || []
})

// 通知消息
const notifications = computed(() => {
  return dashboardData.value?.notifications || []
})

// 收益趋势数据
const returnTrends = computed(() => {
  return dashboardData.value?.returnTrends || []
})

// 投资分布数据
const investmentDistribution = computed(() => {
  return dashboardData.value?.investmentDistribution || []
})

// 投资分布图表（短剧项目占比）
let investmentDistributionChart = null
const renderInvestmentDistributionChart = () => {
  const chartDom = document.getElementById('investment-distribution-chart')
  if (!chartDom || !investmentDistribution.value.length) return

  investmentDistributionChart = echarts.init(chartDom)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} 贝壳 ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      data: investmentDistribution.value.map(item => item.projectName)
    },
    series: [
      {
        name: '投资分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: investmentDistribution.value.map(item => ({
          value: item.shellsAmount,
          name: item.projectName,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }

  investmentDistributionChart.setOption(option)
}

// 收益趋势图表（双Y轴）
let returnTrendChart = null
const renderReturnTrendChart = () => {
  const chartDom = document.getElementById('return-trend-chart')
  if (!chartDom || !returnTrends.value.length) return

  returnTrendChart = echarts.init(chartDom)

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          const unit = param.seriesName === '消耗贝壳' ? '贝壳' : '钻石'
          result += `${param.marker}${param.seriesName}: ${param.value.toLocaleString()} ${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['消耗贝壳', '收益钻石'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: returnTrends.value.map(item => item.month)
    },
    yAxis: [
      {
        type: 'value',
        name: '贝壳数量',
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '钻石数量',
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '消耗贝壳',
        type: 'line',
        yAxisIndex: 0,
        data: returnTrends.value.map(item => item.consumedShells),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#3B82F6'
        },
        itemStyle: {
          color: '#3B82F6'
        }
      },
      {
        name: '收益钻石',
        type: 'line',
        yAxisIndex: 1,
        data: returnTrends.value.map(item => item.earnedDiamonds),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#10B981'
        },
        itemStyle: {
          color: '#10B981'
        }
      }
    ]
  }

  returnTrendChart.setOption(option)
}

// 项目进度图表
let projectProgressChart = null
const renderProjectProgressChart = () => {
  const chartDom = document.getElementById('project-progress-chart')
  if (!chartDom || !userProjects.value.length) return

  projectProgressChart = echarts.init(chartDom)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    yAxis: {
      type: 'category',
      data: userProjects.value.map(p => p.name),
      inverse: true
    },
    series: [{
      name: '完成进度',
      type: 'bar',
      data: userProjects.value.map(p => p.progress),
      label: {
        show: true,
        position: 'right',
        formatter: '{c}%'
      },
      itemStyle: {
        color: function(params: any) {
          // 根据进度设置不同颜色
          const progress = params.data;
          if (progress < 30) return '#3B82F6';
          if (progress < 70) return '#10B981';
          return '#F59E0B';
        }
      }
    }]
  }

  projectProgressChart.setOption(option)
}

// 标记通知为已读
const markAsRead = (id: number) => {
  const notification = notifications.value.find(item => item.id === id)
  if (notification) {
    notification.read = true
  }
}

// 标记所有通知为已读
const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    notification.read = true
  })
}

// 计算未读通知数量
const unreadCount = computed(() => {
  return notifications.value.filter(item => !item.read).length
})

// 处理窗口大小变化
const handleResize = () => {
  if (investmentDistributionChart) investmentDistributionChart.resize();
  if (returnTrendChart) returnTrendChart.resize();
  if (projectProgressChart) projectProgressChart.resize();
};

// 监听数据变化，重新渲染图表
const renderCharts = () => {
  nextTick(() => {
    renderInvestmentDistributionChart();
    renderReturnTrendChart();
    renderProjectProgressChart();
  });
}

// 监听数据变化
watch(dashboardData, (newData) => {
  if (newData) {
    renderCharts();
  }
}, { deep: true });

// 初始化函数
onMounted(async () => {
  // 确保认证数据已初始化
  authStore.initAuth();

  // 如果用户未登录，跳转到登录页
  if (!authStore.isLoggedIn) {
    router.push('/login');
    return;
  }

  // 获取仪表板数据
  await fetchDashboardData();

  // 渲染图表
  renderCharts();

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (investmentDistributionChart) investmentDistributionChart.dispose();
  if (returnTrendChart) returnTrendChart.dispose();
  if (projectProgressChart) projectProgressChart.dispose();
});
</script>

<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 个人中心内容 -->
    <div class="container mx-auto px-4 py-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- 主要内容 -->
      <div v-else class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 左侧：个人信息卡片 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm p-6 relative">
            <!-- 退出登录按钮 - 右上角 -->
            <button
              @click="logout"
              class="absolute top-4 right-4 text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
              title="退出登录"
            >
              <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>

            <div class="text-center">
              <!-- 用户头像 -->
              <div class="mb-4">
                <UserAvatar
                  :username="userInfo.name"
                  size="xl"
                  class="mx-auto"
                />
              </div>

              <!-- 用户基本信息 -->
              <h3 class="text-xl font-semibold text-gray-900 mb-1">{{ userInfo.name }}</h3>
              <p class="text-sm text-gray-600 mb-2">{{ userInfo.level }}</p>

              <!-- 等级徽章 -->
              <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                   :class="{
                     'bg-yellow-100 text-yellow-800': userInfo.level.includes('钻石'),
                     'bg-gray-100 text-gray-800': userInfo.level.includes('白金'),
                     'bg-yellow-50 text-yellow-700': userInfo.level.includes('金牌'),
                     'bg-gray-50 text-gray-700': userInfo.level.includes('银牌'),
                     'bg-blue-50 text-blue-700': userInfo.level.includes('普通')
                   }">
                {{ userInfo.level }}
              </div>

              <!-- 最后登录时间 -->
              <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-xs text-gray-500">最后登录</p>
                <p class="text-sm text-gray-700">{{ userInfo.lastLogin }}</p>
              </div>
            </div>
          </div>

          <!-- 9宫格功能卡片 -->
          <GridCard class="mt-6" />

          <!-- 最新通知模块 - 移动到个人信息卡片下面 -->
          <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold">最新通知</h2>
              <div class="flex items-center space-x-2">
                <!-- 通知铃铛图标 -->
                <div class="relative">
                  <button class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200">
                    <svg class="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                    <span v-if="unreadCount > 0" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {{ unreadCount }}
                    </span>
                  </button>
                </div>
                <button
                  @click="markAllAsRead"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  全部已读
                </button>
              </div>
            </div>

            <div v-if="notifications.length > 0" class="space-y-3">
              <div
                v-for="notification in notifications.slice(0, 3)"
                :key="notification.id"
                class="flex items-start p-3 rounded-lg"
                :class="notification.read ? 'bg-white' : 'bg-blue-50'"
              >
                <div class="flex-shrink-0 mr-3">
                  <div
                    class="w-8 h-8 rounded-full flex items-center justify-center"
                    :class="{
                      'bg-blue-100 text-blue-600': notification.type === '系统通知',
                      'bg-green-100 text-green-600': notification.type === '项目进展',
                      'bg-amber-100 text-amber-600': notification.type === '收益发放'
                    }"
                  >
                    <svg v-if="notification.type === '系统通知'" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <svg v-else-if="notification.type === '项目进展'" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                    <svg v-else-if="notification.type === '收益发放'" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>

                <div class="flex-grow min-w-0">
                  <div class="flex justify-between items-start">
                    <span class="text-xs text-gray-500">{{ notification.type }}</span>
                    <span class="text-xs text-gray-500">{{ notification.date }}</span>
                  </div>
                  <p class="font-medium text-sm truncate" :class="notification.read ? 'text-gray-700' : 'text-gray-900'">
                    {{ notification.title }}
                  </p>
                  <p v-if="notification.message" class="text-xs text-gray-600 mt-1 line-clamp-2">
                    {{ notification.message }}
                  </p>
                </div>

                <button
                  v-if="!notification.read"
                  @click="markAsRead(notification.id)"
                  class="ml-2 text-blue-600 hover:text-blue-800 flex-shrink-0"
                >
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </button>
              </div>
            </div>

            <div v-else class="text-center py-6 text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              <p class="text-sm font-medium">暂无通知</p>
              <p class="text-xs">您的通知将在这里显示</p>
            </div>

            <div v-if="notifications.length > 3" class="mt-4 text-center">
              <a href="#" class="text-blue-600 hover:text-blue-800 text-sm font-medium">查看全部通知</a>
            </div>
          </div>
        </div>

        <!-- 右侧：数据统计区域 -->
        <div class="lg:col-span-3">
          <!-- 数据卡片区域 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 所剩投资贝壳卡片 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <p class="text-sm text-gray-500 mb-1">所剩投资贝壳</p>
                  <p class="text-2xl font-bold text-blue-600">{{ formatNumber(calculateRemainingShells()) }}</p>
                  <p class="text-xs text-gray-500 mt-1">总投资: {{ formatNumber(assetOverview.totalShells) }}</p>

                  <!-- 充值按钮和刷新按钮 -->
                  <div class="mt-3 flex items-center justify-between">
                    <button
                      @click="openRechargeDialog"
                      class="px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      充值
                    </button>
                    <button
                      @click="refreshAssetData"
                      class="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                      title="刷新数据"
                    >
                      <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div class="p-3 bg-blue-100 rounded-lg">
                  <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 已消耗贝壳卡片 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <div class="flex justify-between items-start">
                <div>
                  <p class="text-sm text-gray-500 mb-1">已消耗贝壳</p>
                  <p class="text-2xl font-bold text-orange-600">{{ formatNumber(assetOverview.consumedShells) }}</p>
                  <p class="text-xs text-gray-500 mt-1">投资短剧数: {{ assetOverview.projectsCount }}</p>
                </div>
                <div class="p-3 bg-orange-100 rounded-lg">
                  <svg class="w-6 h-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 累计收益钻石卡片 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <div class="flex justify-between items-start">
                <div>
                  <p class="text-sm text-gray-500 mb-1">累计收益钻石</p>
                  <p class="text-2xl font-bold text-green-600">{{ formatNumber(assetOverview.totalDiamonds) }}</p>
                </div>
                <div class="p-3 bg-green-100 rounded-lg">
                  <svg class="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 收益率卡片 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <div class="flex justify-between items-start">
                <div>
                  <p class="text-sm text-gray-500 mb-1">收益率</p>
                  <p class="text-2xl font-bold text-purple-600">{{ calculateReturnRate() }}%</p>
                  <p class="text-xs text-gray-500 mt-1">(收益钻石-消耗贝壳)÷消耗贝壳</p>
                </div>
                <div class="p-3 bg-purple-100 rounded-lg">
                  <svg class="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 收益趋势图 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-lg font-semibold mb-4">收益趋势</h2>
              <div id="return-trend-chart" class="w-full h-80"></div>
            </div>

            <!-- 投资分布图 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-lg font-semibold mb-4">投资分布</h2>
              <div id="investment-distribution-chart" class="w-full h-80"></div>
            </div>
          </div>

          <!-- 项目进度 -->
          <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold">投资项目进度</h2>
              <button @click="showInvestmentRecords" class="text-blue-600 hover:text-blue-800 text-sm font-medium">查看投资记录</button>
            </div>

            <div v-if="userProjects.length > 0">
              <div id="project-progress-chart" class="w-full h-64 mb-6"></div>

              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投资贝壳</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开始日期</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结束日期</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已获钻石</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预期总钻石</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="project in userProjects" :key="project.id">
                      <td class="px-4 py-4 whitespace-nowrap font-medium">{{ project.name }}</td>
                      <td class="px-4 py-4 whitespace-nowrap">{{ formatNumber(project.investAmount) }}</td>
                      <td class="px-4 py-4 whitespace-nowrap">{{ project.startDate }}</td>
                      <td class="px-4 py-4 whitespace-nowrap">{{ project.endDate }}</td>
                      <td class="px-4 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded-full"
                              :class="getStatusColorClass(project.status)">
                          {{ project.status }}
                        </span>
                      </td>
                      <td class="px-4 py-4 whitespace-nowrap text-green-600">{{ formatNumber(project.returns) }}</td>
                      <td class="px-4 py-4 whitespace-nowrap">{{ formatNumber(project.expectedTotal) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div v-else class="text-center py-8 text-gray-500">
              <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
              </svg>
              <p class="text-lg font-medium">暂无投资项目</p>
              <p class="text-sm">开始您的第一笔投资吧</p>
              <router-link to="/projects" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                浏览项目
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 充值弹窗 -->
    <SimpleRechargeDialog
      :visible="showRechargeDialog"
      @close="closeRechargeDialog"
      @success="onRechargeSuccess"
    />

  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>