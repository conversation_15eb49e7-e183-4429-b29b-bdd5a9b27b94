/**
 * 认证工具函数
 */
import { useRouter } from 'vue-router'
import Swal from 'sweetalert2'

/**
 * 检查用户是否已登录
 */
export const isLoggedIn = (): boolean => {
  const token = localStorage.getItem('token')
  const user = localStorage.getItem('user')
  return !!(token && user)
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    return userStr ? JSON.parse(userStr) : null
  } catch (error) {
    console.error('解析用户信息失败:', error)
    return null
  }
}

/**
 * 获取认证令牌
 */
export const getAuthToken = (): string | null => {
  return localStorage.getItem('token')
}

/**
 * 清除认证信息
 */
export const clearAuth = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
}

/**
 * 检查登录状态，如果未登录则显示提示并跳转到登录页
 */
export const requireLogin = (router?: any): boolean => {
  if (!isLoggedIn()) {
    Swal.fire({
      title: '请先登录',
      text: '此功能需要登录后才能使用',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: '去登录',
      cancelButtonText: '取消'
    }).then((result) => {
      if (result.isConfirmed && router) {
        router.push('/login')
      }
    })
    return false
  }
  return true
}

/**
 * 处理认证错误
 */
export const handleAuthError = (error: any, router?: any) => {
  if (error.response?.status === 401) {
    clearAuth()
    Swal.fire({
      title: '登录已过期',
      text: '请重新登录',
      icon: 'warning',
      confirmButtonText: '去登录'
    }).then(() => {
      if (router) {
        router.push('/login')
      }
    })
  }
}

/**
 * 创建带认证头的请求配置
 */
export const createAuthConfig = () => {
  const token = getAuthToken()
  return token ? {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  } : {}
}

/**
 * 快速退出登录（无确认提示）
 */
export const quickLogout = async (authStore: any, router?: any) => {
  try {
    await authStore.quickLogout()
  } catch (error) {
    console.error('快速退出失败:', error)
    // 即使失败也清除本地状态
    clearAuth()
    if (router) {
      router.push('/')
    }
  }
}

/**
 * 带确认的退出登录
 */
export const confirmLogout = async (authStore: any) => {
  const result = await Swal.fire({
    title: '确认退出',
    text: '确定要退出吗？',
    icon: 'question',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: '确认退出',
    cancelButtonText: '取消'
  })

  if (result.isConfirmed) {
    await authStore.logout()
  }
}
