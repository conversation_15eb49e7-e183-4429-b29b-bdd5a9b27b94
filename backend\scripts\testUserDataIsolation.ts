/**
 * 测试用户数据隔离脚本
 * 验证不同用户登录后看到的是各自独立的数据
 */

import { query } from '../utils/database'

async function testUserDataIsolation() {
  console.log('开始测试用户数据隔离...\n')

  try {
    // 测试用户列表
    const testUsers = [3, 4, 5, 6]

    for (const userId of testUsers) {
      console.log(`=== 用户 ${userId} 的数据 ===`)

      // 查询用户资产
      const assetQuery = `
        SELECT 
          shells_balance,
          diamonds_balance,
          total_invested_shells,
          total_earned_diamonds
        FROM user_assets 
        WHERE user_id = ?
      `
      const assetRows = await query(assetQuery, [userId])
      
      if (assetRows.length > 0) {
        const asset = assetRows[0]
        console.log(`贝壳余额: ${asset.shells_balance}`)
        console.log(`钻石余额: ${asset.diamonds_balance}`)
        console.log(`总投资贝壳: ${asset.total_invested_shells}`)
        console.log(`总收益钻石: ${asset.total_earned_diamonds}`)
      } else {
        console.log('❌ 用户资产数据不存在')
      }

      // 查询投资记录数量
      const investmentQuery = `
        SELECT COUNT(*) as count, SUM(investment_amount) as total_amount
        FROM user_investments 
        WHERE user_id = ?
      `
      const investmentRows = await query(investmentQuery, [userId])
      if (investmentRows.length > 0) {
        const investment = investmentRows[0]
        console.log(`投资记录数: ${investment.count}`)
        console.log(`投资总额: ${investment.total_amount || 0}`)
      }

      // 查询资产变动记录数量
      const transactionQuery = `
        SELECT COUNT(*) as count
        FROM user_asset_transactions 
        WHERE user_id = ?
      `
      const transactionRows = await query(transactionQuery, [userId])
      if (transactionRows.length > 0) {
        console.log(`资产变动记录数: ${transactionRows[0].count}`)
      }

      console.log('') // 空行分隔
    }

    // 验证数据隔离
    console.log('=== 数据隔离验证 ===')
    let isolationPassed = true

    for (let i = 0; i < testUsers.length; i++) {
      for (let j = i + 1; j < testUsers.length; j++) {
        const user1 = testUsers[i]
        const user2 = testUsers[j]

        // 比较资产数据
        const asset1 = await query('SELECT * FROM user_assets WHERE user_id = ?', [user1])
        const asset2 = await query('SELECT * FROM user_assets WHERE user_id = ?', [user2])

        if (asset1.length > 0 && asset2.length > 0) {
          const isSame = (
            asset1[0].shells_balance === asset2[0].shells_balance &&
            asset1[0].diamonds_balance === asset2[0].diamonds_balance &&
            asset1[0].total_invested_shells === asset2[0].total_invested_shells &&
            asset1[0].total_earned_diamonds === asset2[0].total_earned_diamonds
          )

          if (isSame) {
            console.log(`❌ 用户 ${user1} 和用户 ${user2} 的资产数据相同！`)
            isolationPassed = false
          } else {
            console.log(`✅ 用户 ${user1} 和用户 ${user2} 的资产数据不同`)
          }
        }
      }
    }

    if (isolationPassed) {
      console.log('\n🎉 数据隔离测试通过！不同用户的数据是独立的。')
    } else {
      console.log('\n❌ 数据隔离测试失败！存在用户数据共享的问题。')
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testUserDataIsolation().then(() => {
    console.log('测试完成')
    process.exit(0)
  }).catch(error => {
    console.error('测试失败:', error)
    process.exit(1)
  })
}

export { testUserDataIsolation }
