import { query as dbQuery } from '~/utils/database';
import { validateId } from '~/utils/validators';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';
import { logger, logAuditAction, getClientIP } from '~/utils/logger';
import { kickOutUser } from '~/utils/websocket';

/**
 * 管理员修改用户状态接口
 * PUT /api/admin/user-management/[id]/status
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法修改用户状态'
      });
    }

    // 获取用户ID
    const userId = validateId(getRouterParam(event, 'id'));
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的用户ID'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { status } = body;

    // 验证状态值
    if (status !== 'active' && status !== 'inactive') {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的状态值，只能是 active 或 inactive'
      });
    }

    // 查询用户是否存在
    const users = await dbQuery(
      'SELECT id, username, status FROM users WHERE id = ?',
      [userId]
    ) as any[];

    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '用户不存在'
      });
    }

    const user = users[0];
    const statusValue = status === 'active' ? 1 : 0;

    // 检查状态是否有变化
    if (user.status === statusValue) {
      return {
        success: true,
        message: '用户状态无需更改'
      };
    }

    // 更新用户状态
    await dbQuery(
      'UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?',
      [statusValue, userId]
    );

    // 如果用户被封禁，通过WebSocket踢出用户
    if (status === 'inactive') {
      const kickSuccess = kickOutUser(userId, '您的账户已被管理员禁用，请联系客服了解详情');

      logger.info('用户封禁WebSocket通知', {
        userId,
        username: user.username,
        kickSuccess,
        adminId: admin.id,
        adminUsername: admin.username
      });
    }

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_UPDATE_USER_STATUS',
      description: `管理员修改用户状态: ${user.username} -> ${status}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: {
        targetUserId: userId,
        targetUsername: user.username,
        oldStatus: user.status === 1 ? 'active' : 'inactive',
        newStatus: status
      }
    });

    logger.info('管理员修改用户状态成功', {
      adminId: admin.id,
      adminUsername: admin.username,
      targetUserId: userId,
      targetUsername: user.username,
      newStatus: status
    });

    return {
      success: true,
      message: '用户状态修改成功'
    };

  } catch (error: any) {
    logger.error('修改用户状态失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      userId: getRouterParam(event, 'id'),
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
