/**
 * 用户资产相关工具函数
 */

import { query } from './database'

/**
 * 确保用户资产记录存在，如果不存在则创建
 * @param userId 用户ID
 * @returns 用户资产信息
 */
export async function ensureUserAssets(userId: number) {
  try {
    // 查询用户资产
    let assetRows = await query(`
      SELECT 
        shells_balance,
        diamonds_balance,
        total_invested_shells,
        total_earned_diamonds,
        frozen_shells,
        frozen_diamonds
      FROM user_assets 
      WHERE user_id = ?
    `, [userId]);

    if (!assetRows || assetRows.length === 0) {
      // 用户资产记录不存在，创建初始记录
      console.log(`用户 ${userId} 资产记录不存在，创建初始记录`);
      
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance, 
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId]);

      // 重新查询创建的记录
      assetRows = await query(`
        SELECT 
          shells_balance,
          diamonds_balance,
          total_invested_shells,
          total_earned_diamonds,
          frozen_shells,
          frozen_diamonds
        FROM user_assets 
        WHERE user_id = ?
      `, [userId]);
    }

    return assetRows[0];
  } catch (error) {
    console.error(`确保用户 ${userId} 资产记录失败:`, error);
    throw error;
  }
}

/**
 * 获取用户资产信息（简化版，只包含余额相关字段）
 * @param userId 用户ID
 * @returns 用户资产信息
 */
export async function getUserAssetBalance(userId: number) {
  try {
    const asset = await ensureUserAssets(userId);
    return {
      shellsBalance: parseFloat(asset.shells_balance),
      diamondsBalance: parseFloat(asset.diamonds_balance),
      totalInvestedShells: parseFloat(asset.total_invested_shells),
      totalEarnedDiamonds: parseFloat(asset.total_earned_diamonds),
      frozenShells: parseFloat(asset.frozen_shells),
      frozenDiamonds: parseFloat(asset.frozen_diamonds),
      availableShells: parseFloat(asset.shells_balance) - parseFloat(asset.frozen_shells)
    };
  } catch (error) {
    console.error(`获取用户 ${userId} 资产余额失败:`, error);
    throw error;
  }
}

/**
 * 创建初始用户资产记录
 * @param userId 用户ID
 */
export async function createInitialUserAssets(userId: number) {
  try {
    // 检查是否已存在
    const existingAssets = await query(
      'SELECT id FROM user_assets WHERE user_id = ?',
      [userId]
    );

    if (existingAssets.length > 0) {
      console.log(`用户 ${userId} 的资产记录已存在，跳过创建`);
      return;
    }

    // 创建初始资产记录
    await query(`
      INSERT INTO user_assets (
        user_id, shells_balance, diamonds_balance, 
        total_invested_shells, total_earned_diamonds,
        frozen_shells, frozen_diamonds, created_at, updated_at
      ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
    `, [userId]);

    console.log(`为用户 ${userId} 创建初始资产记录成功`);
  } catch (error) {
    console.error(`为用户 ${userId} 创建初始资产记录失败:`, error);
    throw error;
  }
}
