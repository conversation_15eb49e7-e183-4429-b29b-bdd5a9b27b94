<template>
  <div>
    <Page auto-content-height>
      <!-- 系统设置卡片 -->
      <div class="card-box p-6">
        <!-- 设置导航 -->
        <div class="border-b border-gray-200 mb-6">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === tab.key
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ tab.name }}
            </button>
          </nav>
        </div>

        <!-- 设置内容区域 -->
        <!-- 加载中状态 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <Spin />
        </div>

        <!-- 网站设置 -->
        <SiteSettings v-if="activeTab === 'site' && !loading" />

        <!-- 投资设置 -->
        <InvestmentSettings v-if="activeTab === 'investment' && !loading" />

        <!-- 邮件设置 -->
        <EmailSettings v-if="activeTab === 'email' && !loading" />

        <!-- 短信设置 -->
        <SmsSettings v-if="activeTab === 'sms' && !loading" />

        <!-- 安全设置 -->
        <SecuritySettings v-if="activeTab === 'security' && !loading" />

        <!-- 对象存储设置 -->
        <CosSettings v-if="activeTab === 'cos' && !loading" />

        <!-- 联系方式设置 -->
        <ContactSettings v-if="activeTab === 'contact' && !loading" />
      </div>
    </Page>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { Page } from '@vben/common-ui';
import { Spin } from 'ant-design-vue';

// 导入设置组件
import SiteSettings from './modules/SiteSettings.vue';
import InvestmentSettings from './modules/InvestmentSettings.vue';
import EmailSettings from './modules/EmailSettings.vue';
import SmsSettings from './modules/SmsSettings.vue';
import SecuritySettings from './modules/SecuritySettings.vue';
import CosSettings from './modules/CosSettings.vue';
import ContactSettings from './modules/ContactSettings.vue';

// 状态
const loading = ref(false);
const activeTab = ref('site');

// 标签页配置
const tabs = [
  { key: 'site', name: '网站设置' },
  { key: 'investment', name: '投资设置' },
  { key: 'contact', name: '联系方式设置' },
  { key: 'email', name: '邮件设置' },
  { key: 'sms', name: '短信设置' },
  { key: 'security', name: '安全设置' },
  { key: 'cos', name: '对象存储' },
];

onMounted(() => {
  // 初始化页面
  console.log('系统设置页面已挂载，当前选项卡:', activeTab.value);
});
</script>
