<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../store/auth'
import { getSiteSettings, type SiteSettings } from '../services/settingsService'

const router = useRouter()
const authStore = useAuthStore()

const isMenuOpen = ref(false)
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

// 使用Pinia获取登录状态和用户信息
const isLoggedIn = computed(() => authStore.isLoggedIn)
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)

// 网站设置
const siteSettings = ref<SiteSettings>({
  title: '剧投投',
  description: '中国最专业的短剧投资平台',
  logo: '/images/jutoutou-logo.svg',
  favicon: '/favicon.ico',
  icp: '',
  copyright: ''
})

// 加载网站设置
onMounted(async () => {
  try {
    const settings = await getSiteSettings()
    if (settings) {
      siteSettings.value = settings
    }
  } catch (error) {
    console.error('获取网站设置失败:', error)
  }
})

// 进入用户仪表盘
const goToDashboard = () => {
  router.push('/dashboard')
}

// 进入管理员仪表盘
const goToAdminDashboard = () => {
  router.push('/admin')
}
</script>

<template>
  <header class="bg-white border-b border-gray-200 fixed top-0 left-0 right-0 z-50 w-full" style="position: fixed !important; top: 0 !important; z-index: 9999 !important;">
    <div class="container mx-auto px-4 py-4">
      <div class="flex justify-between items-center">
        <!-- Logo -->
        <div class="flex items-center">
          <RouterLink to="/" class="flex items-center">
            <img v-if="siteSettings.logo" :src="siteSettings.logo" class="h-10 w-auto mr-2" alt="Logo" />
            <svg v-else class="h-10 w-auto mr-2" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <!-- 背景矩形 - 移除圆角 -->
              <rect x="2" y="2" width="36" height="36" fill="#7B5AFA" stroke="#6039E4" stroke-width="1"/>

              <!-- 剧字设计 - 戏剧面具 -->
              <circle cx="20" cy="18" r="7" fill="white" opacity="0.9"/>
              <path d="M16 16 Q20 14 24 16" stroke="#7B5AFA" stroke-width="1.5" fill="none"/>
              <path d="M16 20 Q20 22 24 20" stroke="#7B5AFA" stroke-width="1.5" fill="none"/>
              <circle cx="18" cy="17" r="1" fill="#7B5AFA"/>
              <circle cx="22" cy="17" r="1" fill="#7B5AFA"/>

              <!-- 投字设计 - 箭头向上 -->
              <path d="M28 12 L32 8 L36 12 M32 8 L32 24" stroke="white" stroke-width="2" fill="none"/>
              <circle cx="32" cy="26" r="2" fill="white"/>

              <!-- 文字部分 -->
              <text x="45" y="16" font-family="PingFang SC, sans-serif" font-size="12" font-weight="bold" fill="#333">剧投投</text>
              <text x="45" y="28" font-family="PingFang SC, sans-serif" font-size="8" font-weight="500" fill="#7B5AFA">短剧投资</text>
            </svg>
            <span class="text-xl font-bold text-gradient">{{ siteSettings.title }}</span>
          </RouterLink>
        </div>
        
        <!-- 左侧导航项 -->
        <nav class="hidden md:flex items-center space-x-8 flex-grow ml-10">
          <RouterLink to="/" class="nav-link">首页</RouterLink>
          <RouterLink to="/projects" class="nav-link">短剧筹募</RouterLink>
          <RouterLink to="/investment" class="nav-link">投资方案</RouterLink>
          <RouterLink to="/funds" class="nav-link">旅文基金</RouterLink>
          <RouterLink to="/studio" class="nav-link">承制厂牌</RouterLink>
          <RouterLink to="/agency" class="nav-link">演艺经纪</RouterLink>
        </nav>
        
        <!-- 右侧导航项 -->
        <div class="hidden md:flex items-center space-x-6">
          <!-- 已登录显示用户信息，未登录显示登录按钮 -->
          <template v-if="isLoggedIn">
            <div class="flex items-center">
              <button @click="goToDashboard" class="flex items-center space-x-2 group">
                <div class="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white">
                  {{ user?.username ? user.username.charAt(0).toUpperCase() : 'U' }}
                </div>
                <span class="text-gray-700 group-hover:text-primary transition-colors">
                  {{ user?.username || '用户' }}
                </span>
              </button>
            </div>
          </template>
          <template v-else>
            <RouterLink to="/login" class="btn btn-primary">投资者登录</RouterLink>
          </template>
          
          <!-- Language Switcher -->
          <div class="flex items-center">
            <button class="text-primary hover:text-primary-dark">中文</button>
            <span class="mx-2 text-gray-400">|</span>
            <button class="text-gray-600 hover:text-gray-900">EN</button>
          </div>
        </div>
        
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button 
            @click="toggleMenu" 
            class="text-primary hover:text-primary-dark focus:outline-none"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path 
                v-if="!isMenuOpen" 
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M4 6h16M4 12h16M4 18h16"
              />
              <path 
                v-else 
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Mobile Navigation -->
      <div 
        v-show="isMenuOpen" 
        class="md:hidden mt-4 py-2 border-t border-gray-200"
      >
        <RouterLink to="/" class="block py-2 text-gray-700 hover:text-primary">首页</RouterLink>
        <RouterLink to="/projects" class="block py-2 text-gray-700 hover:text-primary">短剧筹募</RouterLink>
        <RouterLink to="/investment" class="block py-2 text-gray-700 hover:text-primary">投资方案</RouterLink>
        <RouterLink to="/funds" class="block py-2 text-gray-700 hover:text-primary">旅文基金</RouterLink>
        <RouterLink to="/studio" class="block py-2 text-gray-700 hover:text-primary">承制厂牌</RouterLink>
        <RouterLink to="/agency" class="block py-2 text-gray-700 hover:text-primary">演艺经纪</RouterLink>
        
        <!-- 移动端登录状态 -->
        <template v-if="isLoggedIn">
          <div class="py-2">
            <button @click="goToDashboard" class="flex items-center space-x-2">
              <div class="w-6 h-6 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white text-xs">
                {{ user?.username ? user.username.charAt(0).toUpperCase() : 'U' }}
              </div>
              <span class="text-primary">{{ user?.username || '用户' }}</span>
            </button>
          </div>
        </template>
        <template v-else>
          <RouterLink to="/login" class="block py-2 text-primary font-medium">投资者登录</RouterLink>
        </template>
        
        <!-- Mobile Language Switcher -->
        <div class="flex space-x-4 py-2">
          <button class="text-primary font-medium">中文</button>
          <button class="text-gray-500">EN</button>
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped>
.nav-link {
  @apply text-gray-700 hover:text-primary transition-colors duration-300 font-medium;
}
.nav-link.router-link-active {
  @apply text-primary font-bold;
}
.text-gradient {
  @apply text-primary font-bold;
}
.btn-primary {
  @apply px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 font-medium;
}
</style>