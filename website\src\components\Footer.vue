<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getSiteSettings, type SiteSettings } from '../services/settingsService'
import { getContactSettings, type ContactSettings } from '../services/contactService'

// 网站设置
const siteSettings = ref<SiteSettings>({
  title: '剧投投',
  description: '中国最专业的短剧投资平台',
  logo: '/images/jutoutou-logo.svg',
  favicon: '/favicon.ico',
  icp: '辽ICP备2024031485号-2',
  copyright: '© 2024-2025 大连幻象文化娱乐有限公司. 保留所有权利'
})

// 联系方式设置
const contactSettings = ref<ContactSettings>({
  onlineQrCode: '',
  paymentQrCode: '',
  contactText: '欢迎联系我们，我们将竭诚为您服务',
  contactAddress: '',
  contactEmail: '',
  contactPhone: ''
})

// 加载设置
onMounted(async () => {
  try {
    // 加载网站设置
    const settings = await getSiteSettings()
    if (settings) {
      siteSettings.value = settings
    }

    // 加载联系方式设置
    const contact = await getContactSettings()
    if (contact) {
      contactSettings.value = contact
    }
  } catch (error) {
    console.error('获取设置失败:', error)
  }
})
</script>

<template>
  <footer class="bg-gray-800 text-white pt-12 pb-8">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- 公司简介 -->
        <div>
          <h3 class="text-xl font-semibold mb-4 text-gradient-white">{{ siteSettings.title }}</h3>
          <p class="text-gray-300 mb-4">{{ siteSettings.description }}</p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-300 hover:text-white">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-300 hover:text-white">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-300 hover:text-white">
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
              </svg>
            </a>
          </div>
        </div>
        
        <!-- 快速链接 -->
        <div>
          <h3 class="text-xl font-semibold mb-4 text-gradient-white">快速链接</h3>
          <ul class="space-y-2">
            <li><RouterLink to="/" class="text-gray-300 hover:text-white">首页</RouterLink></li>
            <li><RouterLink to="/investment" class="text-gray-300 hover:text-white">投资方案</RouterLink></li>
            <li><RouterLink to="/team" class="text-gray-300 hover:text-white">团队介绍</RouterLink></li>
            <li><RouterLink to="/news" class="text-gray-300 hover:text-white">新闻动态</RouterLink></li>
          </ul>
        </div>
        
        <!-- 联系我们 -->
        <div>
          <h3 class="text-xl font-semibold mb-4 text-gradient-white">联系我们</h3>
          <ul class="space-y-2">
            <li v-if="contactSettings.contactAddress" class="flex items-start">
              <svg class="h-6 w-6 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span class="text-gray-300">{{ contactSettings.contactAddress }}</span>
            </li>
            <li v-if="contactSettings.contactEmail" class="flex items-start">
              <svg class="h-6 w-6 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span class="text-gray-300">{{ contactSettings.contactEmail }}</span>
            </li>
            <li v-if="contactSettings.contactPhone" class="flex items-start">
              <svg class="h-6 w-6 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              <span class="text-gray-300">{{ contactSettings.contactPhone }}</span>
            </li>
            <!-- 如果没有设置联系信息，显示联系我们文字 -->
            <li v-if="!contactSettings.contactAddress && !contactSettings.contactEmail && !contactSettings.contactPhone && contactSettings.contactText" class="text-gray-300">
              {{ contactSettings.contactText }}
            </li>
          </ul>
        </div>
        
        <!-- 订阅通讯 -->
        <div>
          <h3 class="text-xl font-semibold mb-4 text-gradient-white">订阅通讯</h3>
          <p class="text-gray-300 mb-4">订阅我们的通讯，获取最新短剧行业动态和投资机会。</p>
          <form class="flex flex-col sm:flex-row gap-2">
            <input type="email" placeholder="您的邮箱地址" class="px-4 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary" />
            <button type="submit" class="btn btn-primary whitespace-nowrap">订阅</button>
          </form>
        </div>
      </div>
      
      <div class="border-t border-gray-700 mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm">{{ siteSettings.copyright }}</p>
        <div class="mt-4 md:mt-0">
          <span href="https://beian.miit.gov.cn/" class="text-gray-400 text-sm hover:text-white ml-4">{{ siteSettings.icp }}</span>
          <a href="#" class="text-gray-400 text-sm hover:text-white ml-4">隐私政策</a>
          <a href="#" class="text-gray-400 text-sm hover:text-white ml-4">服务条款</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.text-gradient-white {
  background: linear-gradient(to right, #FFFFFF, #E0E0E0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.btn-primary {
  @apply px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-md shadow-md hover:shadow-lg transition-all duration-300 font-medium;
}
</style>