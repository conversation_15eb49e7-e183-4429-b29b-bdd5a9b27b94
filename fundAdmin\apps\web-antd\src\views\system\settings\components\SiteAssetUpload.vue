<template>
  <div class="site-asset-upload">
    <!-- URL输入框 -->
    <div class="url-input-section mb-3">
      <a-input
        v-model:value="manualUrl"
        :placeholder="`请输入${assetTypeLabel}地址`"
        @blur="handleManualUrlChange"
        :disabled="uploading"
      >
        <template #addonBefore>
          <IconifyIcon icon="ant-design:link-outlined" />
        </template>
      </a-input>
    </div>

    <!-- 上传和预览区域 -->
    <div class="upload-preview-section">
      <div class="flex space-x-4">
        <!-- 上传区域 -->
        <div class="upload-area flex-1">
          <a-upload-dragger
            :show-upload-list="false"
            :before-upload="beforeUpload"
            :custom-request="handleCustomRequest"
            :disabled="uploading || !storageAvailable"
            class="upload-dragger"
          >
            <div class="upload-content">
              <div v-if="uploading" class="uploading-state">
                <IconifyIcon icon="ant-design:loading-outlined" class="text-2xl text-primary mb-2" />
                <p class="text-sm">上传中... {{ uploadProgress }}%</p>
              </div>
              <div v-else-if="!storageAvailable" class="disabled-state">
                <IconifyIcon icon="ant-design:exclamation-circle-outlined" class="text-2xl text-gray-400 mb-2" />
                <p class="text-sm text-gray-500">对象存储未配置</p>
                <p class="text-xs text-gray-400">请先在对象存储设置中配置并启用</p>
              </div>
              <div v-else class="normal-state">
                <IconifyIcon icon="ant-design:inbox-outlined" class="text-3xl text-gray-400 mb-2" />
                <p class="text-sm">点击或拖拽文件到此区域上传</p>
                <p class="text-xs text-gray-500 mt-1">
                  支持格式：{{ allowedTypesText }}，大小限制：{{ maxSizeText }}
                </p>
              </div>
            </div>
          </a-upload-dragger>
        </div>

        <!-- 图片预览区域 -->
        <div v-if="currentUrl" class="preview-area">
          <div class="preview-container">
            <div class="preview-image-wrapper">
              <img
                v-if="!imageLoadError"
                :src="imageDisplayUrl"
                :alt="assetType"
                :class="previewClass"
                class="preview-image border border-gray-200 rounded"
                @error="handleImageError"
                @load="handleImageLoad"
              />
              <div
                v-else
                class="preview-image preview-error border border-gray-200 rounded"
                :class="previewClass"
              >
                <IconifyIcon icon="ant-design:picture-outlined" class="text-gray-400 text-2xl" />
                <p class="text-xs text-gray-500 mt-1">图片加载失败</p>
              </div>
            </div>
            <div class="preview-actions mt-2">
              <a-button
                size="small"
                danger
                @click="handleRemove"
                :disabled="uploading"
                block
              >
                删除
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { 
  Upload as AUpload, 
  UploadDragger as AUploadDragger,
  Input as AInput,
  Button as AButton,
  message 
} from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';
import { uploadSiteAsset, checkStorageAvailability } from '#/api/system/settings';
import type { SiteAssetType } from '#/api/system/settings/types';

interface Props {
  assetType: SiteAssetType;
  modelValue: string;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'upload-success', data: any): void;
  (e: 'upload-error', error: Error): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 状态
const uploading = ref(false);
const uploadProgress = ref(0);
const storageAvailable = ref(true);
const manualUrl = ref('');
const imageLoadError = ref(false);
const imageRefreshKey = ref(0);

// 当前URL
const currentUrl = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
});

// 图片显示URL（添加刷新参数避免缓存）
const imageDisplayUrl = computed(() => {
  if (!currentUrl.value) return '';

  // 如果刷新键为0，直接返回原URL
  if (imageRefreshKey.value === 0) {
    return currentUrl.value;
  }

  try {
    const url = new URL(currentUrl.value);
    url.searchParams.set('t', imageRefreshKey.value.toString());
    return url.toString();
  } catch (error) {
    // 如果URL格式不正确，直接返回原URL加时间戳
    const separator = currentUrl.value.includes('?') ? '&' : '?';
    return `${currentUrl.value}${separator}t=${imageRefreshKey.value}`;
  }
});

// 资源类型标签
const assetTypeLabel = computed(() => {
  switch (props.assetType) {
    case 'logo': return 'Logo';
    case 'favicon': return '网站图标';
    case 'icon': return '图标';
    case 'qrcode': return '二维码';
    default: return '资源';
  }
});

// 预览样式类
const previewClass = computed(() => {
  switch (props.assetType) {
    case 'logo': return 'h-12 max-w-32';
    case 'favicon': return 'h-8 w-8';
    case 'icon': return 'h-10 w-10';
    case 'qrcode': return 'h-20 w-20';
    default: return 'h-10';
  }
});

// 允许的文件类型
const allowedTypes = computed(() => {
  switch (props.assetType) {
    case 'logo': return ['.png', '.svg', '.jpg', '.jpeg', '.gif'];
    case 'favicon': return ['.ico', '.png', '.svg'];
    case 'icon': return ['.png', '.svg', '.jpg', '.jpeg'];
    case 'qrcode': return ['.png', '.jpg', '.jpeg'];
    default: return ['.png', '.jpg', '.jpeg'];
  }
});

const allowedTypesText = computed(() => allowedTypes.value.join(', '));

// 文件大小限制
const maxSize = 2 * 1024 * 1024; // 2MB
const maxSizeText = '2MB';

// 检查对象存储可用性
const checkStorage = async () => {
  try {
    const response = await checkStorageAvailability();
    storageAvailable.value = response?.isEnabled || false;
  } catch (error) {
    console.warn('检查对象存储可用性失败:', error);
    storageAvailable.value = false;
  }
};

// 文件上传前检查
const beforeUpload = (file: File) => {
  // 检查文件类型
  const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!allowedTypes.value.includes(fileExt)) {
    message.error(`不支持的文件类型，请选择 ${allowedTypesText.value} 格式的文件`);
    return false;
  }

  // 检查文件大小
  if (file.size > maxSize) {
    message.error(`文件大小不能超过 ${maxSizeText}`);
    return false;
  }

  return true; // 允许上传，使用自定义请求处理
};

// 处理文件上传
const handleUpload = async (file: File) => {
  if (!storageAvailable.value) {
    message.error('对象存储未配置，请先在对象存储设置中配置并启用');
    return;
  }

  uploading.value = true;
  uploadProgress.value = 0;

  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('assetType', props.assetType);

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 20;
      }
    }, 200);

    const response = await uploadSiteAsset(formData);

    clearInterval(progressInterval);
    uploadProgress.value = 100;

    if (response && response.url) {
      currentUrl.value = response.url;
      manualUrl.value = response.url;
      // 刷新图片显示
      imageRefreshKey.value = Date.now();
      imageLoadError.value = false;
      message.success(`${assetTypeLabel.value}上传成功`);
      emit('upload-success', response);
    } else {
      throw new Error('上传响应格式错误');
    }
  } catch (error: any) {
    message.error(error.message || `${assetTypeLabel.value}上传失败`);
    emit('upload-error', error);
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
  }
};

// 处理自定义上传请求
const handleCustomRequest = (options: any) => {
  const { file } = options;
  if (file) {
    handleUpload(file);
  }
};

// 处理删除
const handleRemove = () => {
  currentUrl.value = '';
  manualUrl.value = '';
  message.success(`${assetTypeLabel.value}已删除`);
};

// 处理手动输入URL变更
const handleManualUrlChange = () => {
  if (manualUrl.value !== currentUrl.value) {
    currentUrl.value = manualUrl.value;
    // 刷新图片显示
    imageRefreshKey.value = Date.now();
    imageLoadError.value = false;
  }
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
  console.error('图片加载失败:', {
    displayUrl: imageDisplayUrl.value,
    originalUrl: currentUrl.value,
    refreshKey: imageRefreshKey.value,
    event
  });
  imageLoadError.value = true;

  // 尝试测试原始URL是否可访问
  if (currentUrl.value && imageRefreshKey.value > 0) {
    console.log('尝试使用原始URL:', currentUrl.value);
    imageRefreshKey.value = 0; // 重置为0，使用原始URL
  }
};

// 处理图片加载成功
const handleImageLoad = (event: Event) => {
  console.log('图片加载成功:', {
    displayUrl: imageDisplayUrl.value,
    originalUrl: currentUrl.value,
    refreshKey: imageRefreshKey.value
  });
  imageLoadError.value = false;
};

// 监听modelValue变化，同步到manualUrl
watch(() => props.modelValue, (newValue) => {
  manualUrl.value = newValue;
}, { immediate: true });

// 监听currentUrl变化，重置图片加载状态
watch(() => currentUrl.value, (newValue) => {
  if (newValue) {
    imageLoadError.value = false;
    // 如果URL变化，重置刷新键
    if (imageRefreshKey.value === 0) {
      imageRefreshKey.value = 1;
    }
  }
}, { immediate: true });

// 组件挂载时检查存储可用性
onMounted(() => {
  checkStorage();
});
</script>

<style scoped>
.site-asset-upload {
  width: 100%;
}

.url-input-section {
  width: 100%;
}

.upload-preview-section {
  width: 100%;
}

.upload-area {
  min-width: 0; /* 防止flex子项溢出 */
}

.upload-dragger {
  background: #fafafa !important;
  height: 120px;
}

.upload-content {
  padding: 16px;
  text-align: center;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploading-state,
.disabled-state,
.normal-state {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-area {
  width: 120px;
  flex-shrink: 0;
}

.preview-container {
  text-align: center;
}

.preview-image-wrapper {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background: #fafafa;
}

.preview-error {
  width: 100%;
  height: 100%;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-actions {
  width: 100%;
}

/* Logo特殊样式 */
.preview-image.logo {
  height: 60px;
}

/* 图标特殊样式 */
.preview-image.favicon,
.preview-image.icon {
  height: 40px;
  width: 40px;
  margin: 0 auto;
}
</style>
