/**
 * 获取用户仪表板数据接口
 * GET /api/users/dashboard
 */

import { query } from '~/utils/database'
import { verifyUserAccessToken } from '~/utils/auth'

export default defineEventHandler(async (event) => {
  try {
    console.log('Dashboard API called');

    // 验证用户认证
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户认证失败，请重新登录'
      });
    }

    const userId = userPayload.id;

    // 从数据库获取用户资产数据
    const assetQuery = `
      SELECT
        shells_balance,
        diamonds_balance,
        total_invested_shells,
        total_earned_diamonds,
        frozen_shells,
        frozen_diamonds
      FROM user_assets
      WHERE user_id = ?
    `;

    const assetRows = await query(assetQuery, [userId]);
    console.log('Asset rows:', assetRows);
    const assetData = assetRows[0] || {
      shells_balance: 0,
      diamonds_balance: 0,
      total_invested_shells: 0,
      total_earned_diamonds: 0,
      frozen_shells: 0,
      frozen_diamonds: 0
    };

    // 获取用户投资项目数据 - 按项目合并
    const projectsQuery = `
      SELECT
        project_id,
        project_name as name,
        SUM(investment_amount) as investAmount,
        MIN(DATE_FORMAT(start_date, '%Y-%m-%d')) as startDate,
        MAX(DATE_FORMAT(end_date, '%Y-%m-%d')) as endDate,
        CASE
          WHEN MAX(project_status) = 'active' THEN '进行中'
          WHEN MAX(project_status) = 'completed' THEN '已完成'
          WHEN MAX(project_status) = 'paused' THEN '暂停'
          ELSE '未知'
        END as status,
        AVG(progress) as progress,
        SUM(COALESCE(actual_return_amount, 0)) as returns,
        SUM(expected_return_amount) as expectedTotal,
        COUNT(*) as investmentCount
      FROM user_investments
      WHERE user_id = ? AND investment_status = 'active'
      GROUP BY project_id, project_name
      ORDER BY MIN(investment_date) DESC
    `;

    const projectRows = await query(projectsQuery, [userId]);

    // 获取通知数据
    const notificationsQuery = `
      SELECT
        id,
        type,
        title,
        content as message,
        DATE_FORMAT(created_at, '%Y-%m-%d') as date,
        is_read as is_read_status
      FROM user_notifications
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 10
    `;

    const notificationRows = await query(notificationsQuery, [userId]);

    // 获取收益趋势数据 - 按月统计
    const trendsQuery = `
      SELECT
        DATE_FORMAT(created_at, '%Y-%m') as month,
        DATE_FORMAT(created_at, '%m月') as monthLabel,
        SUM(CASE WHEN transaction_type = 'shells_out' AND related_type = 'investment' THEN amount ELSE 0 END) as consumedShells,
        SUM(CASE WHEN transaction_type = 'diamonds_in' AND related_type = 'return' THEN amount ELSE 0 END) as earnedDiamonds
      FROM user_asset_transactions
      WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month ASC
    `;

    const trendsRows = await query(trendsQuery, [userId]);

    // 计算所剩投资贝壳
    const totalInvested = parseFloat(assetData.total_invested_shells);
    const shellsBalance = parseFloat(assetData.shells_balance);
    const totalEarned = parseFloat(assetData.total_earned_diamonds);

    const remainingShells = shellsBalance;
    const consumedShells = totalInvested - shellsBalance;

    // 计算收益率：(收益钻石 - 消耗贝壳) / 消耗贝壳
    const returnRate = consumedShells > 0 ? ((totalEarned - consumedShells) / consumedShells * 100) : 0;

    const dashboardData = {
      // 资产概览 - 使用真实数据
      assetOverview: {
        totalShells: totalInvested,                        // 总投资贝壳数
        consumedShells: consumedShells,                    // 已消耗贝壳数
        totalDiamonds: totalEarned,                        // 累计收益钻石数
        returnRate: parseFloat(returnRate.toFixed(2)),     // 收益率
        availableShells: shellsBalance,                    // 可用贝壳数（所剩投资贝壳）
        frozenShells: parseFloat(assetData.frozen_shells), // 冻结贝壳数
        projectsCount: projectRows.length,                 // 投资项目数
        monthlyDiamonds: 25000                             // 本月钻石收益（暂时保持模拟数据）
      },

      // 用户投资项目 - 使用真实数据
      userProjects: projectRows.map(project => ({
        ...project,
        investAmount: parseFloat(project.investAmount),
        returns: parseFloat(project.returns),
        expectedTotal: parseFloat(project.expectedTotal),
        fundCode: `FD${String(project.id).padStart(3, '0')}`,
        projectType: '短剧',
        riskLevel: '中等',
        currentValue: parseFloat(project.investAmount) + parseFloat(project.returns),
        totalReturn: parseFloat(project.returns),
        monthlyReturn: Math.round(parseFloat(project.returns) / Math.max(1, project.progress / 100 * 12))
      })),

      // 收益趋势数据（支持双Y轴）- 使用真实数据
      returnTrends: trendsRows.map(trend => ({
        month: trend.monthLabel,
        consumedShells: parseFloat(trend.consumedShells) || 0,
        earnedDiamonds: parseFloat(trend.earnedDiamonds) || 0
      })),

      // 投资分布（短剧项目占比）- 使用真实数据
      investmentDistribution: (() => {
        const totalInvestment = projectRows.reduce((sum, project) => sum + parseFloat(project.investAmount), 0);
        const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316'];

        return projectRows.map((project, index) => ({
          projectName: project.name,
          shellsAmount: parseFloat(project.investAmount),
          percentage: totalInvestment > 0 ? parseFloat(((parseFloat(project.investAmount) / totalInvestment) * 100).toFixed(1)) : 0,
          color: colors[index % colors.length]
        }));
      })(),

      // 风险分布
      riskDistribution: [
        { level: '低风险', amount: 400000, percentage: 33.3, color: '#10B981' },
        { level: '中等风险', amount: 500000, percentage: 41.7, color: '#F59E0B' },
        { level: '高风险', amount: 300000, percentage: 25.0, color: '#EF4444' }
      ],

      // 最近交易记录
      recentTransactions: [
        {
          id: 1,
          type: '投资',
          project: '《奇幻世界》系列',
          amount: 300000,
          date: '2023-09-01',
          status: '已完成',
          description: '投资短剧项目'
        },
        {
          id: 2,
          type: '收益',
          project: '《青春有你》系列',
          amount: 8000,
          date: '2023-10-01',
          status: '已到账',
          description: '月度收益分红'
        }
      ],

      // 通知消息 - 使用真实数据
      notifications: notificationRows.map(notification => ({
        ...notification,
        type: notification.type === 'system' ? '系统通知' :
              notification.type === 'project' ? '项目进展' :
              notification.type === 'return' ? '收益发放' :
              notification.type === 'investment' ? '投资机会' : '其他',
        read: notification.is_read_status === 1
      })),

      // 市场概览
      marketOverview: {
        totalMarketSize: '50亿',
        yearGrowthRate: '25%',
        activeProjects: 156,
        totalInvestors: 8520,
        averageReturn: '18.5%',
        successRate: '92%'
      },

      // 充值记录
      rechargeRecords: [],

      // 提现记录
      withdrawRecords: []
    };

    return {
      success: true,
      data: dashboardData
    };

  } catch (error: any) {
    console.error('获取用户仪表板数据失败:', error);

    // 返回错误响应
    return {
      success: false,
      error: '服务器内部错误'
    };
  }
});
