<template>
  <div class="settings-container">
    <a-form
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      @finish="handleSubmit"
      class="settings-form"
      layout="horizontal"
    >
      <a-form-item
        label="JWT密钥"
        name="jwtSecret"
        :rules="[
          { required: true, message: '请输入JWT密钥' },
          { min: 32, message: 'JWT密钥长度不能少于32位' }
        ]"
      >
        <a-input v-model:value="formState.jwtSecret" placeholder="请输入至少32位的JWT密钥" />
      </a-form-item>

      <a-form-item
        label="用户JWT过期时间"
        name="jwtExpiresIn"
        :rules="[
          { required: true, message: '请输入用户JWT过期时间' },
          { pattern: /^(\d+)([hdms])$/, message: '请使用正确格式，如：24h, 7d, 30m, 60s' }
        ]"
      >
        <a-input
          v-model:value="formState.jwtExpiresIn"
          placeholder="如：24h（24小时）、7d（7天）、30m（30分钟）、60s（60秒）"
        />
      </a-form-item>

      <a-form-item
        label="管理员JWT过期时间"
        name="jwtAdminExpiresIn"
        :rules="[
          { required: true, message: '请输入管理员JWT过期时间' },
          { pattern: /^(\d+)([hdms])$/, message: '请使用正确格式，如：12h, 1d, 60m, 30s' }
        ]"
      >
        <a-input
          v-model:value="formState.jwtAdminExpiresIn"
          placeholder="如：12h（12小时）、1d（1天）、60m（60分钟）、30s（30秒）"
        />
      </a-form-item>

      <a-form-item
        label="访问令牌密钥"
        name="accessTokenSecret"
        :rules="[
          { required: true, message: '请输入访问令牌密钥' },
          { min: 32, message: '访问令牌密钥长度不能少于32位' }
        ]"
      >
        <a-input v-model:value="formState.accessTokenSecret" placeholder="请输入至少32位的访问令牌密钥" />
      </a-form-item>

      <a-form-item
        label="刷新令牌密钥"
        name="refreshTokenSecret"
        :rules="[
          { required: true, message: '请输入刷新令牌密钥' },
          { min: 32, message: '刷新令牌密钥长度不能少于32位' }
        ]"
      >
        <a-input v-model:value="formState.refreshTokenSecret" placeholder="请输入至少32位的刷新令牌密钥" />
      </a-form-item>

      <a-form-item
        label="密码最小长度"
        name="passwordMinLength"
        :rules="[{ required: true, message: '请输入密码最小长度' }]"
      >
        <a-input-number
          v-model:value="formState.passwordMinLength"
          :min="6"
          :max="20"
          style="width: 100%"
          addon-after="位"
        />
      </a-form-item>

      <a-form-item
        label="最大登录尝试次数"
        name="maxLoginAttempts"
        :rules="[{ required: true, message: '请输入最大登录尝试次数' }]"
      >
        <a-input-number
          v-model:value="formState.maxLoginAttempts"
          :min="3"
          :max="10"
          style="width: 100%"
          addon-after="次"
        />
      </a-form-item>

      <a-form-item
        label="账户锁定时间"
        name="lockTime"
        :rules="[{ required: true, message: '请输入账户锁定时间' }]"
      >
        <a-input-number
          v-model:value="formState.lockTime"
          :min="5"
          :max="1440"
          style="width: 100%"
          addon-after="分钟"
        />
      </a-form-item>

      <a-form-item name="enableCaptcha" :wrapper-col="{ span: 18, offset: 6 }">
        <a-checkbox v-model:checked="formState.enableCaptcha">
          启用验证码
        </a-checkbox>
      </a-form-item>

      <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
        <div class="flex justify-end">
          <a-button type="primary" html-type="submit" :loading="saving">
            {{ saving ? '保存中...' : '保存设置' }}
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import {
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Button as AButton,
  Checkbox as ACheckbox,
  message
} from 'ant-design-vue';
import { getSecuritySettings, updateSecuritySettings } from '#/api/system/settings';
import type { SecuritySettings } from '#/api/system/settings/types';

// 表单状态
const formState = reactive<SecuritySettings>({
  jwtSecret: '',
  jwtExpiresIn: '24h',
  jwtAdminExpiresIn: '12h',
  accessTokenSecret: '',
  refreshTokenSecret: '',
  passwordMinLength: 8,
  enableCaptcha: true,
  maxLoginAttempts: 5,
  lockTime: 30,
});

// 保存状态
const saving = ref(false);

// 加载安全设置
const loadSettings = async () => {
  try {
    console.log('开始加载安全设置...');
    const data = await getSecuritySettings();
    console.log('安全设置API响应:', data);

    if (data) {
      console.log('安全设置数据:', data);
      console.log('JWT密钥字段值:', data.jwtSecret);
      Object.assign(formState, data);
      console.log('表单状态更新后:', formState);
    } else {
      console.warn('安全设置响应中没有数据');
    }
  } catch (error: any) {
    console.error('加载安全设置失败:', error);
    message.error(error.message || '加载安全设置失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  saving.value = true;
  try {
    console.log('提交安全设置:', formState);
    await updateSecuritySettings(formState);
    message.success('安全设置保存成功');
    // 重新加载数据以确保显示最新的设置
    await loadSettings();
  } catch (error: any) {
    console.error('保存安全设置失败:', error);
    message.error(error.message || '保存安全设置失败');
  } finally {
    saving.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadSettings();
});
</script>

<style scoped>
@import '../styles/common.css';
</style>
