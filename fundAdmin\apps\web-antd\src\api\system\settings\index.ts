/**
 * 系统设置API
 */
import { requestClient } from '#/api/request';
import type {
  ApiResponse,
  SiteSettings,
  InvestmentSettings,
  EmailSettings,
  SmsSettings,
  SecuritySettings,
  CosSettings,
  ContactSettings,
  TestEmailParams,
  TestSmsParams,
  UploadResponse,
} from './types';

/**
 * 获取网站设置
 */
export function getSiteSettings() {
  return requestClient.get<SiteSettings>('/admin/settings/site');
}

/**
 * 更新网站设置
 */
export function updateSiteSettings(data: SiteSettings) {
  return requestClient.post<ApiResponse>('/admin/settings/site', data);
}

/**
 * 获取投资设置
 */
export function getInvestmentSettings() {
  return requestClient.get<InvestmentSettings>('/admin/settings/investment');
}

/**
 * 更新投资设置
 */
export function updateInvestmentSettings(data: InvestmentSettings) {
  return requestClient.post<ApiResponse>('/admin/settings/investment', data);
}

/**
 * 获取邮件设置
 */
export function getEmailSettings() {
  return requestClient.get<EmailSettings>('/admin/settings/email');
}

/**
 * 更新邮件设置
 */
export function updateEmailSettings(data: EmailSettings) {
  return requestClient.post<ApiResponse>('/admin/settings/email', data);
}

/**
 * 测试邮件设置
 */
export function testEmailSettings(data: TestEmailParams) {
  return requestClient.post<ApiResponse>('/admin/settings/email/test', data);
}

/**
 * 获取短信设置
 */
export function getSmsSettings() {
  return requestClient.get<SmsSettings>('/admin/settings/sms');
}

/**
 * 更新短信设置
 */
export function updateSmsSettings(data: SmsSettings) {
  return requestClient.post<ApiResponse>('/admin/settings/sms', data);
}

/**
 * 测试短信设置
 */
export function testSmsSettings(data: TestSmsParams) {
  return requestClient.post<ApiResponse>('/admin/settings/sms/test', data);
}

/**
 * 获取安全设置
 */
export function getSecuritySettings() {
  return requestClient.get<SecuritySettings>('/admin/settings/security');
}

/**
 * 更新安全设置
 */
export function updateSecuritySettings(data: SecuritySettings) {
  return requestClient.post<ApiResponse>('/admin/settings/security', data);
}

/**
 * 获取对象存储设置
 */
export function getCosSettings() {
  return requestClient.get<CosSettings>('/admin/settings/cos');
}

/**
 * 更新对象存储设置
 */
export function updateCosSettings(data: CosSettings) {
  return requestClient.post<ApiResponse>('/admin/settings/cos', data);
}

/**
 * 获取联系方式设置
 */
export function getContactSettings() {
  return requestClient.get<ContactSettings>('/admin/settings/contact');
}

/**
 * 更新联系方式设置
 */
export function updateContactSettings(data: ContactSettings) {
  return requestClient.post<ApiResponse>('/admin/settings/contact', data);
}

/**
 * 上传网站资源（Logo、图标等）
 */
export function uploadSiteAsset(formData: FormData) {
  return requestClient.post<UploadResponse>('/admin/upload/site-asset', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 检查对象存储是否可用
 */
export function checkStorageAvailability() {
  return requestClient.get<CosSettings>('/admin/settings/cos');
}

/**
 * 上传网站Logo（兼容性保留）
 */
export function uploadSiteLogo(formData: FormData) {
  return requestClient.post<UploadResponse>('/admin/upload/site/logo', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 上传网站图标（兼容性保留）
 */
export function uploadSiteFavicon(formData: FormData) {
  return requestClient.post<UploadResponse>('/admin/upload/site/favicon', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
