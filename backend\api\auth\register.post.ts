import { 
  findUserByCredentials, 
  hashPassword, 
  generateAccessToken, 
  generateRefreshToken 
} from '~/utils/auth';
import { query } from '~/utils/database';
import { 
  useResponseSuccess, 
  useResponseError, 
  validationErrorResponse,
  serverErrorResponse 
} from '~/utils/response';
import { logger, logAuditAction, extractAuditInfo } from '~/utils/logger';
import { setRefreshTokenCookie } from '~/utils/cookie-utils';

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event);
    const { username, email, phone, password, real_name, user_type } = body;

    // 验证必填字段 - 必须提供邮箱或手机号
    if (!username || !password || (!email && !phone)) {
      return validationErrorResponse(
        event,
        '用户名、密码和联系方式（邮箱或手机号）不能为空',
        {
          username: !username ? '用户名不能为空' : null,
          password: !password ? '密码不能为空' : null,
          contact: (!email && !phone) ? '请提供邮箱或手机号' : null
        }
      );
    }

    // 验证密码强度
    if (password.length < 6) {
      return validationErrorResponse(event, '密码长度不能少于6位');
    }

    // 验证用户类型
    const validUserTypes = ['investor', 'producer', 'fund_manager'];
    const userType = user_type || 'investor'; // 默认为投资者
    if (!validUserTypes.includes(userType)) {
      return validationErrorResponse(event, '无效的用户类型');
    }

    // 检查邮箱是否已存在
    if (email) {
      const emailUser = await findUserByCredentials(email);
      if (emailUser) {
        return useResponseError('邮箱已被注册');
      }
    }

    // 检查手机号是否已存在
    if (phone) {
      const phoneUser = await findUserByCredentials(phone);
      if (phoneUser) {
        return useResponseError('手机号已被注册');
      }
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建用户 - 使用password_hash字段
    const result = await query(
      `INSERT INTO users (username, email, phone, password_hash, real_name, user_type, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, 1, NOW(), NOW())`,
      [username, email || null, phone || null, hashedPassword, real_name || null, userType]
    );

    const userId = (result as any).insertId;

    // 为新用户创建初始资产记录
    try {
      await query(
        `INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())`,
        [userId]
      );
      logger.info('为新用户创建资产记录成功', { userId, username });
    } catch (assetError) {
      logger.error('创建用户资产记录失败', { userId, username, error: assetError });
      // 不抛出错误，因为资产记录可以后续创建
    }

    // 记录注册日志
    await logAuditAction({
      userId,
      username,
      userType: 'user',
      action: 'USER_REGISTER',
      description: '用户注册成功',
      ...extractAuditInfo(event)
    });

    logger.info('用户注册成功', { userId, username });

    // 生成令牌
    const user = {
      id: userId,
      username,
      email,
      phone,
      real_name,
      user_type: userType,
      status: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);

    // 设置刷新令牌到Cookie
    setRefreshTokenCookie(event, refreshToken);

    // 返回用户信息和令牌
    return useResponseSuccess({
      token: accessToken,
      user: {
        ...user,
        accessToken,
        realName: real_name || username,
        roles: ['user'],
        homePath: '/workspace'
      }
    }, '注册成功');

  } catch (error: any) {
    logger.error('注册过程中发生错误', { error: error?.message || error });
    
    // 记录系统错误日志
    await logAuditAction({
      action: 'REGISTER_ERROR',
      description: `注册系统错误: ${error?.message || error}`,
      ...extractAuditInfo(event)
    });

    return serverErrorResponse(event, '注册失败，请稍后重试');
  }
});
