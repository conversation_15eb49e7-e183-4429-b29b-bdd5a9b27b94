/**
 * 系统设置API类型定义
 */

/** 网站设置 */
export interface SiteSettings {
  title: string;
  description: string;
  logo: string;
  favicon: string;
  icp: string;
  copyright: string;
}

/** 投资设置 */
export interface InvestmentSettings {
  minAmount: number;
  maxAmount: number;
  minReturnRate: number;
  platformFee: number;
}

/** 邮件设置 */
export interface EmailSettings {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  from: string;
}

/** 短信设置 */
export interface SmsSettings {
  provider: string;
  accessKey: string;
  accessSecret: string;
  signName: string;
  templateCode: string;
}

/** 安全设置 */
export interface SecuritySettings {
  jwtSecret: string;
  jwtExpiresIn: string;
  jwtAdminExpiresIn: string;
  accessTokenSecret: string;
  refreshTokenSecret: string;
  passwordMinLength: number;
  enableCaptcha: boolean;
  maxLoginAttempts: number;
  lockTime: number;
}

/** 对象存储设置 */
export interface CosSettings {
  provider: string;
  secretId: string;
  secretKey: string;
  bucket: string;
  region: string;
  domain: string;
  directory?: string;
  isEnabled?: boolean;
  isLoaded?: boolean;
}



/** API响应基础类型 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

/** 测试邮件参数 */
export interface TestEmailParams {
  to: string;
  subject: string;
  text: string;
}

/** 测试短信参数 */
export interface TestSmsParams {
  phone: string;
  message: string;
}

/** 文件上传响应 */
export interface UploadResponse {
  url: string;
  filename: string;
  originalname: string;
  mimetype: string;
  size: number;
  assetType?: string;
  provider?: string;
  key?: string;
}

/** 网站资源类型 */
export type SiteAssetType = 'logo' | 'favicon' | 'icon';

/** 文件上传参数 */
export interface FileUploadParams {
  file: File;
  assetType?: SiteAssetType;
  onProgress?: (percent: number) => void;
  onSuccess?: (response: UploadResponse) => void;
  onError?: (error: Error) => void;
}
