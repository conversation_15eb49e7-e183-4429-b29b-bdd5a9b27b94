import { query } from '~/utils/database';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';
import { logger, getClientIP } from '~/utils/logger';

/**
 * 获取联系方式设置接口
 * GET /api/admin/settings/contact
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法访问系统设置'
      });
    }

    // 从数据库获取联系方式设置
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['contact_settings']
    );

    let contactSettings = {};
    
    if (result.length > 0 && result[0].setting_value) {
      try {
        contactSettings = JSON.parse(result[0].setting_value);
      } catch (error) {
        logger.error('解析联系方式设置JSON失败', { error: error.message });
      }
    }

    // 如果没有设置，返回默认值
    if (Object.keys(contactSettings).length === 0) {
      contactSettings = {
        onlineQrCode: '',
        paymentQrCode: '',
        contactText: '欢迎联系我们，我们将竭诚为您服务',
        contactAddress: '',
        contactEmail: '',
        contactPhone: ''
      };
    }

    return {
      success: true,
      data: contactSettings
    };

  } catch (error: any) {
    logger.error('获取联系方式设置失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
