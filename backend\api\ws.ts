/**
 * WebSocket 路由处理器
 * 处理用户和管理员的WebSocket连接
 */
import { defineWebSocketHandler } from 'crossws/utils'
import { verifyUserAccessToken, verifyAdminAccessToken } from '~/utils/auth'
import { 
  addUserConnection, 
  removeUserConnection,
  addAdminConnection,
  removeAdminConnection,
  getOnlineStats
} from '~/utils/websocket'
import { logger } from '~/utils/logger'

export default defineWebSocketHandler({
  async open(peer) {
    try {
      // 从查询参数获取token和类型
      const url = new URL(peer.url || '', 'http://localhost')
      const token = url.searchParams.get('token')
      const type = url.searchParams.get('type') // 'user' 或 'admin'
      
      if (!token || !type) {
        logger.warn('WebSocket连接缺少必要参数', {
          hasToken: !!token,
          type,
          ip: peer.addr
        })
        peer.close(1008, '缺少认证参数')
        return
      }

      // 根据类型验证token
      if (type === 'user') {
        // 验证用户token
        const userPayload = verifyUserToken(token)
        if (!userPayload) {
          logger.warn('用户WebSocket认证失败', {
            token: token.substring(0, 20) + '...',
            ip: peer.addr
          })
          peer.close(1008, '用户认证失败')
          return
        }

        // 存储用户信息到peer上下文
        peer.ctx = {
          type: 'user',
          userId: userPayload.id,
          username: userPayload.username
        }

        // 添加到用户连接映射
        addUserConnection(userPayload.id, peer)
        
        // 发送连接成功消息
        peer.send(JSON.stringify({
          type: 'CONNECTION_SUCCESS',
          data: {
            message: '用户WebSocket连接成功',
            userId: userPayload.id,
            timestamp: new Date().toISOString()
          }
        }))

        logger.info('用户WebSocket连接成功', {
          userId: userPayload.id,
          username: userPayload.username,
          ip: peer.addr
        })

      } else if (type === 'admin') {
        // 验证管理员token
        const adminPayload = verifyAdminToken(token)
        if (!adminPayload) {
          logger.warn('管理员WebSocket认证失败', {
            token: token.substring(0, 20) + '...',
            ip: peer.addr
          })
          peer.close(1008, '管理员认证失败')
          return
        }

        // 存储管理员信息到peer上下文
        peer.ctx = {
          type: 'admin',
          adminId: adminPayload.id,
          username: adminPayload.username
        }

        // 添加到管理员连接映射
        addAdminConnection(adminPayload.id, peer)
        
        // 发送连接成功消息和在线统计
        const stats = getOnlineStats()
        peer.send(JSON.stringify({
          type: 'CONNECTION_SUCCESS',
          data: {
            message: '管理员WebSocket连接成功',
            adminId: adminPayload.id,
            onlineStats: stats,
            timestamp: new Date().toISOString()
          }
        }))

        logger.info('管理员WebSocket连接成功', {
          adminId: adminPayload.id,
          username: adminPayload.username,
          ip: peer.addr,
          onlineStats: stats
        })

      } else {
        logger.warn('WebSocket连接类型无效', {
          type,
          ip: peer.addr
        })
        peer.close(1008, '无效的连接类型')
        return
      }

    } catch (error) {
      logger.error('WebSocket连接处理失败', {
        error: error.message,
        ip: peer.addr
      })
      peer.close(1011, '服务器内部错误')
    }
  },

  async message(peer, message) {
    try {
      const data = JSON.parse(message.text())
      const ctx = peer.ctx as any

      if (!ctx) {
        peer.close(1008, '未认证的连接')
        return
      }

      logger.debug('收到WebSocket消息', {
        type: ctx.type,
        userId: ctx.userId,
        adminId: ctx.adminId,
        messageType: data.type
      })

      // 处理心跳消息
      if (data.type === 'PING') {
        peer.send(JSON.stringify({
          type: 'PONG',
          timestamp: new Date().toISOString()
        }))
        return
      }

      // 处理获取在线统计请求（仅管理员）
      if (data.type === 'GET_ONLINE_STATS' && ctx.type === 'admin') {
        const stats = getOnlineStats()
        peer.send(JSON.stringify({
          type: 'ONLINE_STATS',
          data: stats,
          timestamp: new Date().toISOString()
        }))
        return
      }

      // 其他消息类型可以在这里扩展
      logger.debug('未处理的WebSocket消息类型', {
        messageType: data.type,
        ctx
      })

    } catch (error) {
      logger.error('WebSocket消息处理失败', {
        error: error.message,
        message: message.text()
      })
    }
  },

  async close(peer, details) {
    try {
      const ctx = peer.ctx as any
      
      if (ctx) {
        if (ctx.type === 'user' && ctx.userId) {
          removeUserConnection(ctx.userId, peer)
          logger.info('用户WebSocket连接已关闭', {
            userId: ctx.userId,
            username: ctx.username,
            code: details.code,
            reason: details.reason
          })
        } else if (ctx.type === 'admin' && ctx.adminId) {
          removeAdminConnection(ctx.adminId, peer)
          logger.info('管理员WebSocket连接已关闭', {
            adminId: ctx.adminId,
            username: ctx.username,
            code: details.code,
            reason: details.reason
          })
        }
      }
    } catch (error) {
      logger.error('WebSocket关闭处理失败', {
        error: error.message
      })
    }
  },

  async error(peer, error) {
    logger.error('WebSocket连接错误', {
      error: error.message,
      ctx: peer.ctx
    })
  }
})

/**
 * 验证用户token（简化版本，直接使用JWT验证）
 */
function verifyUserToken(token: string) {
  try {
    const jwt = require('jsonwebtoken')
    const config = useRuntimeConfig()
    const decoded = jwt.verify(token, config.jwtSecret)
    
    if (decoded.type === 'user') {
      return decoded
    }
    return null
  } catch (error) {
    return null
  }
}

/**
 * 验证管理员token（简化版本，直接使用JWT验证）
 */
function verifyAdminToken(token: string) {
  try {
    const jwt = require('jsonwebtoken')
    const config = useRuntimeConfig()
    const decoded = jwt.verify(token, config.jwtSecret)
    
    if (decoded.type === 'admin') {
      return decoded
    }
    return null
  } catch (error) {
    return null
  }
}
