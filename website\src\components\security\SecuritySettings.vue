<template>
  <div class="security-settings">
    <!-- 安全概览 -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">安全概览</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-green-50 p-4 rounded-lg">
          <div class="text-green-600 text-sm font-medium">登录状态</div>
          <div class="text-green-800 text-lg font-semibold">安全</div>
        </div>
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="text-blue-600 text-sm font-medium">活跃设备</div>
          <div class="text-blue-800 text-lg font-semibold">{{ deviceHistory.length }} 台</div>
        </div>
        <div class="bg-yellow-50 p-4 rounded-lg">
          <div class="text-yellow-600 text-sm font-medium">安全等级</div>
          <div class="text-yellow-800 text-lg font-semibold">中等</div>
        </div>
      </div>
    </div>

    <!-- 设备管理 -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h3 class="text-lg font-semibold mb-4">设备管理</h3>
      <div class="space-y-4">
        <div 
          v-for="device in deviceHistory" 
          :key="device.deviceId"
          class="flex items-center justify-between p-4 border rounded-lg"
          :class="device.isCurrentDevice ? 'border-blue-200 bg-blue-50' : 'border-gray-200'"
        >
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
              <i :class="getDeviceIcon(device.deviceType)" class="text-gray-600"></i>
            </div>
            <div>
              <div class="font-medium">{{ device.browser }} on {{ device.os }}</div>
              <div class="text-sm text-gray-500">
                {{ device.isCurrentDevice ? '当前设备' : '最后活跃' }}: 
                {{ formatTime(device.lastActiveTime) }}
              </div>
              <div v-if="device.location" class="text-sm text-gray-500">
                {{ device.location }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span 
              v-if="device.isCurrentDevice" 
              class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
            >
              当前设备
            </span>
            <button 
              v-else
              @click="removeDevice(device.deviceId)"
              class="px-3 py-1 text-red-600 hover:bg-red-50 rounded text-sm"
            >
              移除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全日志 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">安全日志</h3>
        <button 
          @click="clearLogs"
          class="px-3 py-1 text-gray-600 hover:bg-gray-50 rounded text-sm"
        >
          清除日志
        </button>
      </div>
      
      <!-- 日志统计 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900">{{ securityStats.last24Hours }}</div>
          <div class="text-sm text-gray-500">24小时内</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900">{{ securityStats.last7Days }}</div>
          <div class="text-sm text-gray-500">7天内</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-red-600">{{ securityStats.riskLevels.high }}</div>
          <div class="text-sm text-gray-500">高风险事件</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900">{{ securityStats.totalLogs }}</div>
          <div class="text-sm text-gray-500">总日志数</div>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="space-y-3 max-h-96 overflow-y-auto">
        <div 
          v-for="log in recentLogs" 
          :key="log.id"
          class="flex items-center justify-between p-3 border rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div 
              class="w-2 h-2 rounded-full"
              :class="getRiskLevelColor(log.riskLevel)"
            ></div>
            <div>
              <div class="font-medium">{{ getEventTypeText(log.type) }}</div>
              <div class="text-sm text-gray-500">
                {{ formatTime(log.timestamp) }} · {{ log.deviceInfo.browser }} on {{ log.deviceInfo.os }}
              </div>
            </div>
          </div>
          <span 
            class="px-2 py-1 text-xs rounded-full"
            :class="getRiskLevelBadge(log.riskLevel)"
          >
            {{ log.riskLevel }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getDeviceLoginHistory, removeDeviceRecord, type DeviceLoginRecord } from '../../utils/deviceUtils'
import { getSecurityLogs, clearSecurityLogs, getSecurityStats, type SecurityLogEntry } from '../../utils/securityLogger'
import Swal from 'sweetalert2'

// 响应式数据
const deviceHistory = ref<DeviceLoginRecord[]>([])
const securityLogs = ref<SecurityLogEntry[]>([])
const securityStats = ref<any>({})

// 计算属性
const recentLogs = computed(() => securityLogs.value.slice(0, 20))

// 获取设备图标
const getDeviceIcon = (deviceType: string) => {
  switch (deviceType) {
    case 'Mobile': return 'fas fa-mobile-alt'
    case 'Tablet': return 'fas fa-tablet-alt'
    case 'Desktop': return 'fas fa-desktop'
    default: return 'fas fa-question'
  }
}

// 格式化时间
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 获取风险等级颜色
const getRiskLevelColor = (level: string) => {
  switch (level) {
    case 'HIGH': return 'bg-red-500'
    case 'MEDIUM': return 'bg-yellow-500'
    case 'LOW': return 'bg-green-500'
    default: return 'bg-gray-500'
  }
}

// 获取风险等级徽章样式
const getRiskLevelBadge = (level: string) => {
  switch (level) {
    case 'HIGH': return 'bg-red-100 text-red-800'
    case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
    case 'LOW': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// 获取事件类型文本
const getEventTypeText = (type: string) => {
  const typeMap = {
    'LOGIN': '登录',
    'LOGOUT': '登出',
    'LOGIN_FAILED': '登录失败',
    'TOKEN_REFRESH': '令牌刷新',
    'PASSWORD_CHANGE': '密码修改',
    'SUSPICIOUS_ACTIVITY': '可疑活动'
  }
  return typeMap[type] || type
}

// 移除设备
const removeDevice = async (deviceId: string) => {
  const result = await Swal.fire({
    title: '确认移除设备',
    text: '移除后该设备将需要重新登录',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: '确认移除',
    cancelButtonText: '取消'
  })
  
  if (result.isConfirmed) {
    removeDeviceRecord(deviceId)
    loadData()
    Swal.fire('已移除', '设备已成功移除', 'success')
  }
}

// 清除日志
const clearLogs = async () => {
  const result = await Swal.fire({
    title: '确认清除日志',
    text: '此操作将清除所有安全日志记录',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: '确认清除',
    cancelButtonText: '取消'
  })
  
  if (result.isConfirmed) {
    clearSecurityLogs()
    loadData()
    Swal.fire('已清除', '安全日志已清除', 'success')
  }
}

// 加载数据
const loadData = () => {
  deviceHistory.value = getDeviceLoginHistory()
  securityLogs.value = getSecurityLogs()
  securityStats.value = getSecurityStats()
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.security-settings {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
</style>
