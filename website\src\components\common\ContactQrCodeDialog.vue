<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getContactSettings, type ContactSettings } from '../../services/contactService'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

// 联系方式设置
const contactSettings = ref<ContactSettings>({
  onlineQrCode: '',
  paymentQrCode: '',
  contactText: '',
  contactAddress: '',
  contactEmail: '',
  contactPhone: ''
})

// 关闭弹窗
const closeDialog = () => {
  emit('close')
}

// 加载联系方式设置
const loadContactSettings = async () => {
  try {
    const settings = await getContactSettings()
    if (settings) {
      contactSettings.value = settings
    }
  } catch (error) {
    console.error('获取联系方式设置失败:', error)
  }
}

// 组件挂载时加载联系方式设置
onMounted(() => {
  loadContactSettings()
})
</script>

<template>
  <!-- 联系二维码弹窗 -->
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <!-- 弹窗标题 -->
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900">联系我们</h3>
        <button @click="closeDialog" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 在线联系二维码 -->
      <div class="text-center mb-6">
        <div class="bg-gray-100 rounded-lg p-6 mb-4">
          <div class="w-48 h-48 mx-auto bg-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
            <img 
              v-if="contactSettings.onlineQrCode" 
              :src="contactSettings.onlineQrCode" 
              alt="在线联系二维码" 
              class="w-full h-full object-contain rounded-lg"
            />
            <div v-else class="text-center text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <p class="text-sm">在线联系二维码</p>
            </div>
          </div>
        </div>
        
        <p class="text-sm text-gray-600 mb-4">
          {{ contactSettings.contactText || '扫描二维码联系我们' }}
        </p>
      </div>

      <!-- 联系信息 -->
      <div v-if="contactSettings.contactAddress || contactSettings.contactEmail || contactSettings.contactPhone" class="bg-gray-50 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-medium text-gray-900 mb-3">其他联系方式</h4>
        <div class="space-y-2 text-sm text-gray-600">
          <div v-if="contactSettings.contactAddress" class="flex items-start">
            <svg class="w-4 h-4 mr-2 mt-0.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>{{ contactSettings.contactAddress }}</span>
          </div>
          <div v-if="contactSettings.contactEmail" class="flex items-start">
            <svg class="w-4 h-4 mr-2 mt-0.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span>{{ contactSettings.contactEmail }}</span>
          </div>
          <div v-if="contactSettings.contactPhone" class="flex items-start">
            <svg class="w-4 h-4 mr-2 mt-0.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            <span>{{ contactSettings.contactPhone }}</span>
          </div>
        </div>
      </div>

      <!-- 关闭按钮 -->
      <div class="flex justify-center">
        <button
          @click="closeDialog"
          class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        >
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-primary {
  background-color: #7B5AFA;
}

.bg-primary-dark {
  background-color: #6039E4;
}
</style>
