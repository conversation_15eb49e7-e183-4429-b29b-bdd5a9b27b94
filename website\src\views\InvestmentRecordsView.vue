<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">投资记录</h1>
        <p class="mt-2 text-gray-600">查看您的所有投资记录和详细信息</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- 投资记录列表 -->
      <div v-else-if="records.length > 0" class="bg-white rounded-lg shadow-sm overflow-hidden">
        <!-- 表格头部 -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-500 uppercase tracking-wider">
            <div class="col-span-4">项目信息</div>
            <div class="col-span-2">投资金额</div>
            <div class="col-span-2">实际收益</div>
            <div class="col-span-1">收益率</div>
            <div class="col-span-2">状态</div>
            <div class="col-span-1">投资时间</div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="divide-y divide-gray-200">
          <div 
            v-for="record in records" 
            :key="record.id"
            class="px-6 py-4 hover:bg-gray-50 transition-colors"
          >
            <div class="grid grid-cols-12 gap-4 items-center">
              <!-- 项目信息 -->
              <div class="col-span-4">
                <div class="font-medium text-gray-900">{{ record.projectName }}</div>
                <div class="text-sm text-gray-500">{{ record.projectCode }}</div>
                <div class="flex items-center text-xs text-gray-400">
                  <span>投资编号: {{ record.investmentNo }}</span>
                  <button
                    @click="copyToClipboard(record.investmentNo, '投资编号')"
                    class="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="复制投资编号"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 投资金额 -->
              <div class="col-span-2">
                <div class="font-medium text-gray-900">{{ formatNumber(record.investmentAmount) }} 贝壳</div>
                <div class="text-sm text-gray-500">投资金额</div>
              </div>

              <!-- 实际收益 -->
              <div class="col-span-2">
                <div class="font-medium" :class="record.actualReturnAmount > 0 ? 'text-green-600' : 'text-gray-400'">
                  {{ formatNumber(record.actualReturnAmount) }} 钻石
                </div>
                <div class="text-sm text-gray-500">实际收益</div>
              </div>

              <!-- 收益率 -->
              <div class="col-span-1">
                <div class="font-medium" :class="getReturnRateColor(record.returnRate)">
                  {{ record.returnRate }}%
                </div>
              </div>

              <!-- 状态 -->
              <div class="col-span-2">
                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full" :class="getStatusClass(record.investmentStatus)">
                  {{ record.investmentStatus }}
                </span>
                <div class="text-xs text-gray-500 mt-1">{{ record.projectStatus }}</div>
              </div>

              <!-- 投资时间 -->
              <div class="col-span-1">
                <div class="text-sm text-gray-900">{{ record.investmentDate }}</div>
                <div class="text-xs text-gray-500" v-if="record.remainingDays > 0">
                  剩余 {{ record.remainingDays }} 天
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} - 
              {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，
              共 {{ pagination.total }} 条记录
            </div>
            <div class="flex space-x-2">
              <button 
                @click="changePage(pagination.page - 1)"
                :disabled="pagination.page <= 1"
                class="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                上一页
              </button>
              <button 
                @click="changePage(pagination.page + 1)"
                :disabled="pagination.page >= pagination.totalPages"
                class="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="bg-white rounded-lg shadow-sm p-12 text-center">
        <div class="text-gray-400 text-6xl mb-4">📊</div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无投资记录</h3>
        <p class="text-gray-500 mb-6">您还没有进行任何投资，快去发现优质项目吧！</p>
        <router-link 
          to="/projects" 
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          浏览项目
        </router-link>
      </div>

      <!-- 返回按钮 -->
      <div class="mt-8 text-center">
        <button 
          @click="$router.back()" 
          class="inline-flex items-center px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          ← 返回
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { formatNumber } from '../utils/format'
import { getInvestmentRecords } from '../api/dashboard'
import Swal from 'sweetalert2'

// 数据状态
const loading = ref(true)
const records = ref([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 获取投资记录
const fetchInvestmentRecords = async (page = 1) => {
  try {
    loading.value = true
    const response = await getInvestmentRecords(page, pagination.value.pageSize)

    if (response.success) {
      records.value = response.data.records
      pagination.value = response.data.pagination
    } else {
      console.error('获取投资记录失败:', response.message)
    }
  } catch (error) {
    console.error('获取投资记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page: number) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    fetchInvestmentRecords(page)
  }
}

// 获取收益率颜色
const getReturnRateColor = (rate: number) => {
  if (rate > 0) return 'text-green-600'
  if (rate < 0) return 'text-red-600'
  return 'text-gray-400'
}

// 获取状态样式
const getStatusClass = (status: string) => {
  switch (status) {
    case '有效':
      return 'bg-green-100 text-green-800'
    case '已完成':
      return 'bg-blue-100 text-blue-800'
    case '已取消':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string, type: string) => {
  try {
    await navigator.clipboard.writeText(text)

    // 显示成功提示
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 2000,
      timerProgressBar: true
    })

    Toast.fire({
      icon: 'success',
      title: `${type}已复制到剪贴板`
    })
  } catch (error) {
    console.error('复制失败:', error)

    // 显示失败提示
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 2000,
      timerProgressBar: true
    })

    Toast.fire({
      icon: 'error',
      title: '复制失败，请手动复制'
    })
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchInvestmentRecords()
})
</script>
