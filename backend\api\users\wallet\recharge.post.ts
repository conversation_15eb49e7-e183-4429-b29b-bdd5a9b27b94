/**
 * 贝壳充值接口
 * POST /api/users/wallet/recharge
 */

import { query } from '~/utils/database'
import { verifyUserAccessToken } from '~/utils/auth'

export default defineEventHandler(async (event) => {
  try {
    // 验证用户认证
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户认证失败，请重新登录'
      });
    }

    const userId = userPayload.id;

    // 获取请求体数据
    const body = await readBody(event);
    const { amount } = body;

    // 验证请求参数
    if (!amount || amount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '充值金额必须大于0'
      });
    }

    // 获取用户当前资产信息，如果不存在则创建
    let assetRows = await query(`
      SELECT
        shells_balance,
        total_invested_shells
      FROM user_assets
      WHERE user_id = ?
    `, [userId]);

    let currentBalance = 0;
    let currentTotalInvested = 0;

    if (!assetRows || assetRows.length === 0) {
      // 用户资产记录不存在，创建初始记录
      console.log(`用户 ${userId} 资产记录不存在，创建初始记录`);
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId]);

      currentBalance = 0;
      currentTotalInvested = 0;
    } else {
      const currentAsset = assetRows[0];
      currentBalance = parseFloat(currentAsset.shells_balance);
      currentTotalInvested = parseFloat(currentAsset.total_invested_shells);
    }

    const newBalance = currentBalance + parseFloat(amount);
    const newTotalInvested = currentTotalInvested + parseFloat(amount);

    // 生成交易流水号
    const transactionNo = `TXN${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    try {
      // 创建pending状态的充值订单，不立即更新用户余额
      await query(`
        INSERT INTO user_asset_transactions (
          user_id, transaction_type, amount, balance_before, balance_after,
          related_type, description, transaction_no, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        userId,
        'shells_in',
        amount,
        currentBalance,
        currentBalance, // 余额暂时不变，等待管理员确认
        'recharge',
        `线下充值 ${amount} 贝壳`,
        transactionNo,
        'pending'
      ]);

      console.log(`创建pending充值订单: ${transactionNo}, 金额: ${amount}, 用户: ${userId}`);

      return {
        success: true,
        data: {
          orderId: transactionNo,
          amount: parseFloat(amount),
          status: 'pending',
          message: '充值订单创建成功，请联系客服完成支付',
          // 客服微信信息
          customerService: {
            wechatQrCode: 'https://example.com/customer-service-qr.png', // 这里应该是实际的客服微信二维码
            workingHours: '24小时',
            instructions: '请扫描二维码添加客服微信，并发送订单号完成支付'
          }
        }
      };

    } catch (error) {
      throw error;
    }

  } catch (error: any) {
    console.error('充值失败:', error);

    return {
      success: false,
      message: '充值失败',
      error: error.message
    };
  }
});
