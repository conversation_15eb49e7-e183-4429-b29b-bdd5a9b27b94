/**
 * 联系方式设置服务
 */
import axios from 'axios'

// 联系方式设置接口
export interface ContactSettings {
  onlineQrCode: string;        // 在线联系二维码
  paymentQrCode: string;       // 线下支付二维码
  contactText: string;         // 联系我们文字
  contactAddress: string;      // 联系地址
  contactEmail: string;        // 联系邮箱
  contactPhone: string;        // 联系电话
}

// API响应接口
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

/**
 * 获取联系方式设置
 */
export async function getContactSettings(): Promise<ContactSettings | null> {
  try {
    const response = await axios.get<ApiResponse<ContactSettings>>('http://localhost:3001/api/public/settings/contact')
    
    if (response.data.success && response.data.data) {
      return response.data.data
    }
    
    return null
  } catch (error) {
    console.error('获取联系方式设置失败:', error)
    // 返回默认值
    return {
      onlineQrCode: '',
      paymentQrCode: '',
      contactText: '欢迎联系我们，我们将竭诚为您服务',
      contactAddress: '',
      contactEmail: '',
      contactPhone: ''
    }
  }
}
