/**
 * 创建投资接口
 * POST /api/users/investments/create
 */

import { query } from '~/utils/database'
import { verifyUserAccessToken } from '~/utils/auth'

export default defineEventHandler(async (event) => {
  try {
    // 验证用户认证
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户认证失败，请重新登录'
      });
    }

    const userId = userPayload.id;

    // 获取请求体数据
    const body = await readBody(event);
    const { projectId, amount } = body;

    // 验证请求参数
    if (!projectId) {
      throw createError({
        statusCode: 400,
        statusMessage: '项目ID不能为空'
      });
    }

    if (!amount || amount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '投资金额必须大于0'
      });
    }

    // 获取用户当前资产信息，如果不存在则创建
    let assetRows = await query(`
      SELECT
        shells_balance,
        total_invested_shells
      FROM user_assets
      WHERE user_id = ?
    `, [userId]);

    if (!assetRows || assetRows.length === 0) {
      // 用户资产记录不存在，创建初始记录
      console.log(`用户 ${userId} 资产记录不存在，创建初始记录`);
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId]);

      // 重新查询创建的记录
      assetRows = await query(`
        SELECT
          shells_balance,
          total_invested_shells
        FROM user_assets
        WHERE user_id = ?
      `, [userId]);
    }

    const currentAsset = assetRows[0];
    const currentBalance = parseFloat(currentAsset.shells_balance);

    // 检查余额是否足够
    if (currentBalance < amount) {
      return {
        success: false,
        message: '余额不足',
        data: {
          currentBalance,
          requiredAmount: amount,
          shortfall: amount - currentBalance
        }
      };
    }

    // 获取项目信息（从drama_series和drama_funding_info表联查）
    const projectQuery = `
      SELECT
        ds.id, ds.title,
        dfi.min_investment, dfi.funding_goal,
        dfi.current_funding, dfi.status, dfi.expected_return
      FROM drama_series ds
      LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
      WHERE ds.id = ? AND dfi.status = 'published'
    `;
    
    const projectRows = await query(projectQuery, [projectId]);
    if (!projectRows || projectRows.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '项目不存在或已关闭'
      });
    }

    const project = projectRows[0];

    // 处理数据类型和默认值
    let minInvestment = 50000; // 默认最小投资5万贝壳
    let maxInvestment = null;
    let fundingGoal = 0;
    let currentFunding = 0;

    try {
      minInvestment = project.min_investment ? parseFloat(project.min_investment) : 50000;
      maxInvestment = project.max_investment ? parseFloat(project.max_investment) : null;
      fundingGoal = project.funding_goal ? parseFloat(project.funding_goal) : 0;
      currentFunding = project.current_funding ? parseFloat(project.current_funding) : 0;

      // 确保最小投资额合理 - 如果数据异常，使用合理默认值
      if (minInvestment > 500000 || minInvestment <= 0) {
        console.log(`异常的最小投资额 ${minInvestment}，重置为50000`);
        minInvestment = 50000;
      }

      // 检查项目是否已经超募
      if (currentFunding >= fundingGoal && fundingGoal > 0) {
        return {
          success: false,
          message: '项目已完成募资目标，暂停接受新投资',
          data: {
            fundingGoal,
            currentFunding,
            fundingProgress: Math.round((currentFunding / fundingGoal) * 100)
          }
        };
      }
    } catch (parseError) {
      console.error('数据解析错误:', parseError);
      minInvestment = 50000;
    }

    // 处理预期收益率 - 从字符串中提取数字
    let expectedReturnRate = 15.0; // 默认15%
    if (project.expected_return) {
      const returnStr = String(project.expected_return);
      const match = returnStr.match(/(\d+(?:\.\d+)?)/); // 提取第一个数字
      if (match) {
        expectedReturnRate = parseFloat(match[1]);
      }
    }

    // 调试信息
    console.log('项目数据:', {
      id: project.id,
      title: project.title,
      min_investment: minInvestment,
      max_investment: maxInvestment,
      funding_goal: fundingGoal,
      current_funding: currentFunding,
      expected_return: project.expected_return,
      expected_return_rate: expectedReturnRate
    });

    // 验证投资金额范围
    if (amount < minInvestment) {
      return {
        success: false,
        message: `投资金额不能少于 ${minInvestment} 贝壳`,
        data: {
          minInvestment: minInvestment
        }
      };
    }

    if (maxInvestment && amount > maxInvestment) {
      return {
        success: false,
        message: `投资金额不能超过 ${maxInvestment} 贝壳`,
        data: {
          maxInvestment: maxInvestment
        }
      };
    }

    // 检查项目是否还能接受投资
    const remainingAmount = fundingGoal - currentFunding;
    if (amount > remainingAmount) {
      return {
        success: false,
        message: `项目剩余募资额度不足，仅剩 ${remainingAmount} 贝壳`,
        data: {
          remainingAmount
        }
      };
    }

    // 生成投资订单号
    const investmentNo = `INV${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // 计算新余额
    const newBalance = currentBalance - amount;

    try {
      console.log('开始投资事务处理...');

      // 1. 更新用户资产
      console.log('更新用户资产...');
      await query(`
        UPDATE user_assets
        SET
          shells_balance = ?,
          updated_at = NOW()
        WHERE user_id = ?
      `, [newBalance, userId]);

      // 2. 创建投资记录
      console.log('创建投资记录...');
      const investmentResult = await query(`
        INSERT INTO user_investments (
          user_id, project_id, project_name, investment_amount,
          expected_return_rate, expected_return_amount,
          investment_date, start_date, end_date,
          project_status, investment_status, progress,
          created_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 12 MONTH), ?, ?, ?, NOW())
      `, [
        userId,
        projectId,
        project.title,
        amount,
        expectedReturnRate,
        Math.round(amount * expectedReturnRate / 100),
        'active',
        'active',
        0
      ]);

      console.log('投资记录创建成功，ID:', investmentResult.insertId);

      // 3. 插入交易记录
      console.log('插入交易记录...');
      await query(`
        INSERT INTO user_asset_transactions (
          user_id, transaction_type, amount, balance_before, balance_after,
          related_type, related_id, description, transaction_no, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        userId,
        'shells_out',
        amount,
        currentBalance,
        newBalance,
        'investment',
        investmentResult.insertId,
        `投资项目：${project.title}`,
        investmentNo,
        'completed'
      ]);

      // 4. 更新项目募资金额
      console.log('更新项目募资金额...');
      await query(`
        UPDATE drama_funding_info
        SET
          current_funding = current_funding + ?
        WHERE drama_id = ?
      `, [amount, projectId]);

      console.log('投资操作完成成功');

      return {
        success: true,
        data: {
          investmentId: investmentResult.insertId,
          investmentNo,
          projectName: project.title,
          amount,
          newBalance,
          expectedReturn: Math.round(amount * expectedReturnRate / 100),
          message: '投资成功'
        }
      };

    } catch (error) {
      console.error('投资操作失败:', error);
      throw error;
    }

  } catch (error: any) {
    console.error('投资失败:', error);
    
    return {
      success: false,
      message: '投资失败',
      error: error.message
    };
  }
});
