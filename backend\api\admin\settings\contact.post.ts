import { query } from '~/utils/database';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';
import { logger, logAuditAction, getClientIP } from '~/utils/logger';

/**
 * 更新联系方式设置接口
 * POST /api/admin/settings/contact
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法修改系统设置'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { 
      onlineQrCode, 
      paymentQrCode, 
      contactText, 
      contactAddress, 
      contactEmail, 
      contactPhone 
    } = body;

    // 验证邮箱格式（如果提供了邮箱）
    if (contactEmail && contactEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(contactEmail.trim())) {
        throw createError({
          statusCode: 400,
          statusMessage: '邮箱格式不正确'
        });
      }
    }

    // 验证电话号码格式（如果提供了电话）
    if (contactPhone && contactPhone.trim()) {
      // 支持手机号、固话、400电话（含空格和横线分隔符）
      const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}[-\s]?\d{7,8}$|^400[-\s]?\d{3}[-\s]?\d{4}$/;
      const cleanPhone = contactPhone.trim().replace(/\s/g, '');
      if (!phoneRegex.test(contactPhone.trim()) && !phoneRegex.test(cleanPhone)) {
        throw createError({
          statusCode: 400,
          statusMessage: '电话号码格式不正确，支持手机号、固话、400电话等格式'
        });
      }
    }

    // 构建设置对象
    const contactSettings = {
      onlineQrCode: onlineQrCode || '',
      paymentQrCode: paymentQrCode || '',
      contactText: contactText ? contactText.trim() : '',
      contactAddress: contactAddress ? contactAddress.trim() : '',
      contactEmail: contactEmail ? contactEmail.trim() : '',
      contactPhone: contactPhone ? contactPhone.trim() : ''
    };

    // 将设置保存到数据库
    const settingsJson = JSON.stringify(contactSettings);
    
    // 检查设置是否已存在
    const existingSettings = await query(
      'SELECT id FROM system_settings WHERE setting_key = ?',
      ['contact_settings']
    );

    if (existingSettings.length > 0) {
      // 更新现有设置
      await query(
        'UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?',
        [settingsJson, 'contact_settings']
      );
    } else {
      // 插入新设置
      await query(
        'INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
        ['contact_settings', settingsJson]
      );
    }

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_UPDATE_CONTACT_SETTINGS',
      description: '管理员更新联系方式设置',
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: { 
        updatedSettings: {
          ...contactSettings,
          // 不记录敏感信息的完整内容
          contactEmail: contactEmail ? '***@***' : '',
          contactPhone: contactPhone ? '***' : ''
        }
      }
    });

    logger.info('管理员更新联系方式设置成功', {
      adminId: admin.id,
      adminUsername: admin.username,
      hasOnlineQrCode: !!contactSettings.onlineQrCode,
      hasPaymentQrCode: !!contactSettings.paymentQrCode,
      hasContactText: !!contactSettings.contactText,
      hasContactAddress: !!contactSettings.contactAddress,
      hasContactEmail: !!contactSettings.contactEmail,
      hasContactPhone: !!contactSettings.contactPhone
    });

    return {
      success: true,
      message: '联系方式设置更新成功',
      data: contactSettings
    };

  } catch (error: any) {
    logger.error('更新联系方式设置失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
