<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <section class="bg-gray-50 py-4">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary text-white py-12 text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">旅文基金</h1>
          <div class="max-w-6xl mx-auto">
            <p class="text-xl leading-relaxed opacity-90">
              剧投投旅文基金专注于文化旅游产业投资，整合优质资源，推动文旅产业创新发展。我们关注具有文化底蕴和创新潜力的旅游项目，包括特色小镇、主题公园、文化IP开发、数字文旅等多个领域，致力于打造中国文旅产业投资的标杆。
            </p>
          </div>
        </div>

        <!-- 基金数据展示 -->
        <div class="bg-white shadow-md p-8 -mt-4" style="border-radius: 0 0 0.75rem 0.75rem;">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gray-50 rounded-lg p-5 text-center">
              <div class="text-3xl font-bold text-primary mb-2">5亿+</div>
              <div class="text-gray-600">管理资金规模</div>
            </div>
            <div class="bg-gray-50 rounded-lg p-5 text-center">
              <div class="text-3xl font-bold text-primary mb-2">380+</div>
              <div class="text-gray-600">项目数量</div>
            </div>
            <div class="bg-gray-50 rounded-lg p-5 text-center">
              <div class="text-3xl font-bold text-primary mb-2">国企</div>
              <div class="text-gray-600">资金属性</div>
            </div>
          </div>
        </div>
      </div>
    </section>

      <div class="container mx-auto px-4">
      
      <!-- 基金功能导航卡片 -->
      <section class="py-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 基金招募卡片 -->
          <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 flex flex-col">
            <div class="bg-gradient-primary p-6 text-white">
              <div class="flex items-center mb-4">
                <svg class="w-10 h-10 mr-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h2 class="text-2xl font-bold">基金招募</h2>
              </div>
              <p class="text-white opacity-90">面向新投资者的募资入口，探索我们精选的优质基金产品</p>
            </div>
            <div class="p-6 flex-grow">
              <ul class="mb-6 text-gray-700">
                <li class="flex items-center mb-3">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  多种基金类型选择，满足不同风险偏好
                </li>
                <li class="flex items-center mb-3">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  灵活的起投金额与封闭期设置
                </li>
                <li class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  专业团队提供投资咨询服务
                </li>
              </ul>
              <RouterLink to="/funds/recruit" class="block w-full py-3 bg-primary hover:bg-primary-dark text-white text-center rounded-lg transition-colors font-medium">
                查看基金产品
              </RouterLink>
            </div>
          </div>
          
          <!-- 信息披露卡片 -->
          <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 flex flex-col">
            <div class="bg-gray-800 p-6 text-white">
              <div class="flex items-center mb-4">
                <svg class="w-10 h-10 mr-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h2 class="text-2xl font-bold">信息披露</h2>
              </div>
              <p class="text-white opacity-90">需登录验证的合规数据查看，为投资者提供透明信息</p>
            </div>
            <div class="p-6 flex-grow">
              <ul class="mb-6 text-gray-700">
                <li class="flex items-center mb-3">
                  <svg class="w-5 h-5 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  定期资金使用报告与财务披露
                </li>
                <li class="flex items-center mb-3">
                  <svg class="w-5 h-5 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  投资者名录与权益分配明细
                </li>
                <li class="flex items-center">
                  <svg class="w-5 h-5 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  监管合规文件下载
                </li>
              </ul>
              <button @click="handleDisclosureClick" class="block w-full py-3 bg-gray-800 hover:bg-gray-900 text-white text-center rounded-lg transition-colors font-medium">
                登录查看披露信息
              </button>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 业务流程介绍 -->
      <section class="py-8">
        <div class="bg-white rounded-xl shadow-md p-8">
          <h2 class="text-2xl font-bold mb-8 text-center">业务流程</h2>

          <div class="relative">
            <!-- 连接线 -->
            <div class="hidden md:block absolute top-8 left-0 right-0 h-0.5 bg-gray-300" style="margin: 0 12.5% 0 12.5%;"></div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 relative">
              <div class="text-center p-4 relative">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4 relative z-10">1</div>
                <h3 class="font-semibold text-lg mb-2">浏览基金产品</h3>
                <p class="text-gray-600 text-sm">选择符合您偏好的基金产品</p>
              </div>

              <div class="text-center p-4 relative">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4 relative z-10">2</div>
                <h3 class="font-semibold text-lg mb-2">客户认证</h3>
                <p class="text-gray-600 text-sm">完成KYC身份认证与合格客户认证</p>
              </div>

              <div class="text-center p-4 relative">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4 relative z-10">3</div>
                <h3 class="font-semibold text-lg mb-2">对接金牌经理</h3>
                <p class="text-gray-600 text-sm">专业经理一对一服务指导</p>
              </div>

              <div class="text-center p-4 relative">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4 relative z-10">4</div>
                <h3 class="font-semibold text-lg mb-2">不定期披露</h3>
                <p class="text-gray-600 text-sm">定期查看基金运行状况</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      </div>

      <!-- 立即咨询模块 -->
      <section class="bg-gradient-primary text-white py-12">
        <div class="container mx-auto px-4">
          <div class="flex flex-col md:flex-row items-center justify-between">
            <div>
              <h2 class="text-2xl md:text-3xl font-bold mb-2">准备好开始您的投资了吗？</h2>
              <p class="opacity-90">我们的投资顾问随时为您提供专业咨询服务</p>
            </div>
            <div class="mt-6 md:mt-0">
              <button @click="showConsultationModal = true" class="btn bg-white text-primary hover:bg-blue-50 px-8 py-3">
                立即咨询
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 咨询弹窗 -->
      <div v-if="showConsultationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showConsultationModal = false">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4" @click.stop>
          <div class="text-center">
            <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">金牌经理</h3>
            <p class="text-gray-600 mb-4">专业的基金咨询服务，为您提供一对一指导</p>

            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <div class="text-sm text-gray-600 mb-2">扫码添加微信咨询</div>
              <div class="w-32 h-32 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
                <img
                  v-if="contactSettings.onlineQrCode"
                  :src="contactSettings.onlineQrCode"
                  alt="基金咨询微信二维码"
                  class="w-full h-full object-contain rounded-lg"
                />
                <svg v-else class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                </svg>
              </div>
            </div>

            <button @click="showConsultationModal = false" class="w-full py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors">
              关闭
            </button>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getContactSettings } from '../services/contactService'

// 咨询弹窗状态
const showConsultationModal = ref(false)

// 联系方式设置
const contactSettings = ref({
  onlineQrCode: '',
  paymentQrCode: '',
  contactText: '',
  contactAddress: '',
  contactEmail: '',
  contactPhone: ''
})

// 处理信息披露按钮点击
const handleDisclosureClick = (event) => {
  event.preventDefault()
  alert('当前功能暂未开放，敬请期待...')
}

// 加载联系方式设置
const loadContactSettings = async () => {
  try {
    const settings = await getContactSettings()
    if (settings) {
      contactSettings.value = settings
    }
  } catch (error) {
    console.error('获取联系方式设置失败:', error)
  }
}

// 组件挂载时加载联系方式设置
onMounted(() => {
  loadContactSettings()
})
</script>

<style scoped>
.text-gradient {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>