/**
 * 管理员登录接口
 * POST /api/auth/admin/login
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { username, password } = body;

    // 参数验证
    if (!username || !password) {
      throw createError({
        statusCode: 400,
        statusMessage: '用户名和密码不能为空'
      });
    }

    // 查找管理员
    const admin = await findAdminByCredentials(username);
    if (!admin) {
      // 记录审计日志
      await logAuditAction({
        action: 'ADMIN_LOGIN_FAILED',
        description: `管理员登录失败：用户不存在 (${username})`,
        ip: getClientIP(event),
        userAgent: getHeader(event, 'user-agent') || '',
        details: { username }
      });

      throw createError({
        statusCode: 401,
        statusMessage: '用户名或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await verifyPassword(password, admin.password!);
    if (!isValidPassword) {
      // 记录审计日志
      await logAuditAction({
        action: 'ADMIN_LOGIN_FAILED',
        description: `管理员登录失败：密码错误`,
        ip: getClientIP(event),
        userAgent: getHeader(event, 'user-agent') || '',
        userId: admin.id,
        username: admin.username,
        details: { reason: 'wrong_password' }
      });

      throw createError({
        statusCode: 401,
        statusMessage: '用户名或密码错误'
      });
    }

    // 生成令牌
    const accessToken = await generateAdminAccessToken(admin);
    const refreshToken = await generateAdminRefreshToken(admin);

    // 更新最后登录时间
    await updateAdminLastLogin(admin.id);

    // 设置刷新令牌到Cookie
    setRefreshTokenCookie(event, refreshToken);

    // 记录成功登录的审计日志
    await logAuditAction({
      action: 'ADMIN_LOGIN_SUCCESS',
      description: '管理员登录成功',
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      userId: admin.id,
      username: admin.username
    });

    logger.info('管理员登录成功', {
      adminId: admin.id,
      username: admin.username,
      ip: getClientIP(event)
    });

    return {
      success: true,
      message: '登录成功',
      data: {
        accessToken,
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          real_name: admin.real_name,
          avatar: admin.avatar,
          home_path: admin.home_path
        }
      }
    };

  } catch (error: any) {
    logger.error('管理员登录失败', {
      error: error.message,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
