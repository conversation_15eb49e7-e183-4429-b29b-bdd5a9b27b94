/**
 * WebSocket 服务工具
 * 用于实时通知用户状态变更
 */
import type { Peer } from 'crossws'
import { logger } from './logger'

// 用户连接映射：userId -> WebSocket连接
const userConnections = new Map<number, Set<Peer>>()

// 管理员连接映射：adminId -> WebSocket连接
const adminConnections = new Map<number, Set<Peer>>()

/**
 * 添加用户连接
 */
export function addUserConnection(userId: number, peer: Peer) {
  if (!userConnections.has(userId)) {
    userConnections.set(userId, new Set())
  }
  userConnections.get(userId)!.add(peer)
  
  logger.info('用户WebSocket连接已建立', {
    userId,
    totalConnections: userConnections.get(userId)!.size
  })
}

/**
 * 移除用户连接
 */
export function removeUserConnection(userId: number, peer: Peer) {
  const connections = userConnections.get(userId)
  if (connections) {
    connections.delete(peer)
    if (connections.size === 0) {
      userConnections.delete(userId)
    }
    
    logger.info('用户WebSocket连接已断开', {
      userId,
      remainingConnections: connections.size
    })
  }
}

/**
 * 添加管理员连接
 */
export function addAdminConnection(adminId: number, peer: Peer) {
  if (!adminConnections.has(adminId)) {
    adminConnections.set(adminId, new Set())
  }
  adminConnections.get(adminId)!.add(peer)
  
  logger.info('管理员WebSocket连接已建立', {
    adminId,
    totalConnections: adminConnections.get(adminId)!.size
  })
}

/**
 * 移除管理员连接
 */
export function removeAdminConnection(adminId: number, peer: Peer) {
  const connections = adminConnections.get(adminId)
  if (connections) {
    connections.delete(peer)
    if (connections.size === 0) {
      adminConnections.delete(adminId)
    }
    
    logger.info('管理员WebSocket连接已断开', {
      adminId,
      remainingConnections: connections.size
    })
  }
}

/**
 * 向指定用户发送消息
 */
export function sendMessageToUser(userId: number, message: any) {
  const connections = userConnections.get(userId)
  if (!connections || connections.size === 0) {
    logger.warn('用户无活跃WebSocket连接', { userId })
    return false
  }

  const messageStr = JSON.stringify(message)
  let sentCount = 0
  
  for (const peer of connections) {
    try {
      peer.send(messageStr)
      sentCount++
    } catch (error) {
      logger.error('发送WebSocket消息失败', {
        userId,
        error: error.message
      })
      // 移除失效的连接
      connections.delete(peer)
    }
  }
  
  logger.info('WebSocket消息已发送', {
    userId,
    sentCount,
    totalConnections: connections.size,
    messageType: message.type
  })
  
  return sentCount > 0
}

/**
 * 向指定管理员发送消息
 */
export function sendMessageToAdmin(adminId: number, message: any) {
  const connections = adminConnections.get(adminId)
  if (!connections || connections.size === 0) {
    logger.warn('管理员无活跃WebSocket连接', { adminId })
    return false
  }

  const messageStr = JSON.stringify(message)
  let sentCount = 0
  
  for (const peer of connections) {
    try {
      peer.send(messageStr)
      sentCount++
    } catch (error) {
      logger.error('发送WebSocket消息失败', {
        adminId,
        error: error.message
      })
      // 移除失效的连接
      connections.delete(peer)
    }
  }
  
  logger.info('WebSocket消息已发送给管理员', {
    adminId,
    sentCount,
    totalConnections: connections.size,
    messageType: message.type
  })
  
  return sentCount > 0
}

/**
 * 踢出用户（发送强制登出消息）
 */
export function kickOutUser(userId: number, reason: string = '账户已被管理员禁用') {
  const message = {
    type: 'FORCE_LOGOUT',
    data: {
      reason,
      timestamp: new Date().toISOString()
    }
  }
  
  const success = sendMessageToUser(userId, message)
  
  logger.info('用户踢出消息已发送', {
    userId,
    reason,
    success
  })
  
  return success
}

/**
 * 广播消息给所有在线用户
 */
export function broadcastToAllUsers(message: any) {
  let sentCount = 0
  const messageStr = JSON.stringify(message)
  
  for (const [userId, connections] of userConnections) {
    for (const peer of connections) {
      try {
        peer.send(messageStr)
        sentCount++
      } catch (error) {
        logger.error('广播消息失败', {
          userId,
          error: error.message
        })
        connections.delete(peer)
      }
    }
  }
  
  logger.info('广播消息已发送', {
    sentCount,
    totalUsers: userConnections.size,
    messageType: message.type
  })
  
  return sentCount
}

/**
 * 获取在线用户统计
 */
export function getOnlineStats() {
  const onlineUsers = userConnections.size
  const onlineAdmins = adminConnections.size
  const totalUserConnections = Array.from(userConnections.values())
    .reduce((sum, connections) => sum + connections.size, 0)
  const totalAdminConnections = Array.from(adminConnections.values())
    .reduce((sum, connections) => sum + connections.size, 0)
  
  return {
    onlineUsers,
    onlineAdmins,
    totalUserConnections,
    totalAdminConnections
  }
}

/**
 * 清理失效连接
 */
export function cleanupConnections() {
  // 清理用户连接
  for (const [userId, connections] of userConnections) {
    const validConnections = new Set<Peer>()
    for (const peer of connections) {
      try {
        // 尝试发送ping消息检查连接状态
        peer.ping()
        validConnections.add(peer)
      } catch (error) {
        logger.debug('清理失效用户连接', { userId })
      }
    }
    
    if (validConnections.size === 0) {
      userConnections.delete(userId)
    } else {
      userConnections.set(userId, validConnections)
    }
  }
  
  // 清理管理员连接
  for (const [adminId, connections] of adminConnections) {
    const validConnections = new Set<Peer>()
    for (const peer of connections) {
      try {
        peer.ping()
        validConnections.add(peer)
      } catch (error) {
        logger.debug('清理失效管理员连接', { adminId })
      }
    }
    
    if (validConnections.size === 0) {
      adminConnections.delete(adminId)
    } else {
      adminConnections.set(adminId, validConnections)
    }
  }
  
  const stats = getOnlineStats()
  logger.info('WebSocket连接清理完成', stats)
}

// 定期清理失效连接（每5分钟）
setInterval(cleanupConnections, 5 * 60 * 1000)
