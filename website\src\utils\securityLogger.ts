/**
 * 安全日志记录工具
 */
import { getDeviceInfo } from './deviceUtils'

export interface SecurityLogEntry {
  id: string
  type: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED' | 'TOKEN_REFRESH' | 'PASSWORD_CHANGE' | 'SUSPICIOUS_ACTIVITY'
  timestamp: string
  deviceInfo: any
  userAgent: string
  ip?: string
  location?: string
  details?: any
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
}

/**
 * 生成日志ID
 */
const generateLogId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 获取客户端IP（模拟，实际应该从服务器获取）
 */
const getClientIP = async (): Promise<string> => {
  try {
    // 这里可以调用第三方服务获取IP
    // 为了演示，返回模拟IP
    return '127.0.0.1'
  } catch (error) {
    return 'Unknown'
  }
}

/**
 * 获取地理位置（模拟）
 */
const getLocation = async (): Promise<string> => {
  try {
    // 这里可以根据IP获取地理位置
    // 为了演示，返回模拟位置
    return '中国 广东省 深圳市'
  } catch (error) {
    return 'Unknown'
  }
}

/**
 * 记录安全日志
 */
export const logSecurityEvent = async (
  type: SecurityLogEntry['type'],
  details?: any,
  riskLevel: SecurityLogEntry['riskLevel'] = 'LOW'
) => {
  try {
    const deviceInfo = getDeviceInfo()
    const ip = await getClientIP()
    const location = await getLocation()
    
    const logEntry: SecurityLogEntry = {
      id: generateLogId(),
      type,
      timestamp: new Date().toISOString(),
      deviceInfo,
      userAgent: navigator.userAgent,
      ip,
      location,
      details,
      riskLevel
    }
    
    // 保存到本地存储
    const logs = getSecurityLogs()
    logs.unshift(logEntry)
    
    // 只保留最近100条日志
    const limitedLogs = logs.slice(0, 100)
    localStorage.setItem('securityLogs', JSON.stringify(limitedLogs))
    
    // 如果是高风险事件，可以发送到服务器
    if (riskLevel === 'HIGH') {
      await sendSecurityAlert(logEntry)
    }
    
    return logEntry
  } catch (error) {
    console.error('记录安全日志失败:', error)
  }
}

/**
 * 获取安全日志
 */
export const getSecurityLogs = (): SecurityLogEntry[] => {
  try {
    const logs = localStorage.getItem('securityLogs')
    return logs ? JSON.parse(logs) : []
  } catch (error) {
    console.error('获取安全日志失败:', error)
    return []
  }
}

/**
 * 清除安全日志
 */
export const clearSecurityLogs = () => {
  localStorage.removeItem('securityLogs')
}

/**
 * 发送安全警报到服务器
 */
const sendSecurityAlert = async (logEntry: SecurityLogEntry) => {
  try {
    // 这里应该调用后端API发送安全警报
    console.warn('安全警报:', logEntry)
    
    // 示例API调用
    // await fetch('/api/security/alert', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(logEntry)
    // })
  } catch (error) {
    console.error('发送安全警报失败:', error)
  }
}

/**
 * 检测可疑活动
 */
export const detectSuspiciousActivity = () => {
  const logs = getSecurityLogs()
  const recentLogs = logs.filter(log => {
    const logTime = new Date(log.timestamp).getTime()
    const now = Date.now()
    return now - logTime < 24 * 60 * 60 * 1000 // 24小时内
  })
  
  // 检测频繁登录失败
  const failedLogins = recentLogs.filter(log => log.type === 'LOGIN_FAILED')
  if (failedLogins.length >= 5) {
    logSecurityEvent('SUSPICIOUS_ACTIVITY', {
      reason: '频繁登录失败',
      count: failedLogins.length
    }, 'HIGH')
  }
  
  // 检测异常设备登录
  const loginLogs = recentLogs.filter(log => log.type === 'LOGIN')
  const uniqueDevices = new Set(loginLogs.map(log => log.deviceInfo.deviceId))
  if (uniqueDevices.size >= 3) {
    logSecurityEvent('SUSPICIOUS_ACTIVITY', {
      reason: '多设备登录',
      deviceCount: uniqueDevices.size
    }, 'MEDIUM')
  }
}

/**
 * 获取安全统计
 */
export const getSecurityStats = () => {
  const logs = getSecurityLogs()
  const now = Date.now()
  
  const stats = {
    totalLogs: logs.length,
    last24Hours: logs.filter(log => now - new Date(log.timestamp).getTime() < 24 * 60 * 60 * 1000).length,
    last7Days: logs.filter(log => now - new Date(log.timestamp).getTime() < 7 * 24 * 60 * 60 * 1000).length,
    riskLevels: {
      low: logs.filter(log => log.riskLevel === 'LOW').length,
      medium: logs.filter(log => log.riskLevel === 'MEDIUM').length,
      high: logs.filter(log => log.riskLevel === 'HIGH').length
    },
    eventTypes: logs.reduce((acc, log) => {
      acc[log.type] = (acc[log.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }
  
  return stats
}

/**
 * 便捷的日志记录函数
 */
export const securityLogger = {
  login: (details?: any) => logSecurityEvent('LOGIN', details, 'LOW'),
  logout: (details?: any) => logSecurityEvent('LOGOUT', details, 'LOW'),
  loginFailed: (details?: any) => logSecurityEvent('LOGIN_FAILED', details, 'MEDIUM'),
  tokenRefresh: (details?: any) => logSecurityEvent('TOKEN_REFRESH', details, 'LOW'),
  passwordChange: (details?: any) => logSecurityEvent('PASSWORD_CHANGE', details, 'MEDIUM'),
  suspiciousActivity: (details?: any) => logSecurityEvent('SUSPICIOUS_ACTIVITY', details, 'HIGH')
}
