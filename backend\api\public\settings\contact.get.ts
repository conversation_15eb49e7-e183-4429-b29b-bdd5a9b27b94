import { query } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';

/**
 * 获取联系方式设置接口（公共接口）
 * GET /api/public/settings/contact
 */
export default defineEventHandler(async (event) => {
  try {
    // 从数据库获取联系方式设置
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['contact_settings']
    );

    let contactSettings = {};
    
    if (result.length > 0 && result[0].setting_value) {
      try {
        contactSettings = JSON.parse(result[0].setting_value);
      } catch (error) {
        logger.error('解析联系方式设置JSON失败', { error: error.message });
      }
    }

    // 如果没有设置，返回默认值
    if (Object.keys(contactSettings).length === 0) {
      contactSettings = {
        onlineQrCode: '',
        paymentQrCode: '',
        contactText: '欢迎联系我们，我们将竭诚为您服务',
        contactAddress: '',
        contactEmail: '',
        contactPhone: ''
      };
    }

    return {
      success: true,
      data: contactSettings
    };

  } catch (error: any) {
    logger.error('获取联系方式设置失败', {
      error: error.message,
      ip: getClientIP(event)
    });

    // 返回默认值而不是错误，确保前端能正常工作
    return {
      success: true,
      data: {
        onlineQrCode: '',
        paymentQrCode: '',
        contactText: '欢迎联系我们，我们将竭诚为您服务',
        contactAddress: '',
        contactEmail: '',
        contactPhone: ''
      }
    };
  }
});
