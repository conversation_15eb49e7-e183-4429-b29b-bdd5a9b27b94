#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2\node_modules\nitropack\dist\cli\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2\node_modules\nitropack\dist\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2\node_modules\nitropack\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/proc/cygdrive/d/ProjectV/ReelShortFund/backend/node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/cli/node_modules:/proc/cygdrive/d/ProjectV/ReelShortFund/backend/node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/node_modules:/proc/cygdrive/d/ProjectV/ReelShortFund/backend/node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/node_modules:/proc/cygdrive/d/ProjectV/ReelShortFund/backend/node_modules/.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules:/proc/cygdrive/d/ProjectV/ReelShortFund/backend/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/cli/index.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/cli/index.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/cli/index.mjs" $args
  } else {
    & "node$exe"  "$basedir/../.pnpm/nitropack@2.11.13_mysql2@3.14.2_xml2js@0.6.2/node_modules/nitropack/dist/cli/index.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
