/**
 * 更新安全设置接口
 * POST /api/admin/settings/security
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法修改安全设置'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const {
      jwtSecret,
      jwtExpiresIn,
      jwtAdminExpiresIn,
      accessTokenSecret,
      refreshTokenSecret,
      passwordMinLength,
      enableCaptcha,
      maxLoginAttempts,
      lockTime
    } = body;

    // 验证必要字段
    if (!jwtExpiresIn || !jwtAdminExpiresIn || !passwordMinLength || !maxLoginAttempts || !lockTime) {
      throw createError({
        statusCode: 400,
        statusMessage: '请提供完整的安全设置参数'
      });
    }

    // 验证JWT过期时间格式（支持 1h, 24h, 7d, 30m 等格式）
    const timeRegex = /^(\d+)([hdms])$/;
    if (!timeRegex.test(jwtExpiresIn) || !timeRegex.test(jwtAdminExpiresIn)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'JWT过期时间格式错误，请使用如：1h, 24h, 7d, 30m 等格式'
      });
    }

    if (passwordMinLength < 6 || passwordMinLength > 20) {
      throw createError({
        statusCode: 400,
        statusMessage: '密码最小长度必须在6-20位之间'
      });
    }

    if (maxLoginAttempts < 3 || maxLoginAttempts > 10) {
      throw createError({
        statusCode: 400,
        statusMessage: '最大登录尝试次数必须在3-10次之间'
      });
    }

    if (lockTime < 5 || lockTime > 1440) {
      throw createError({
        statusCode: 400,
        statusMessage: '账户锁定时间必须在5-1440分钟之间'
      });
    }

    // 获取现有设置以保留JWT密钥（如果没有提供新密钥）
    let existingSettings = {};
    try {
      const result = await query(
        'SELECT setting_value FROM system_settings WHERE setting_key = ?',
        ['security_settings']
      );
      
      if (result.length > 0 && result[0].setting_value) {
        existingSettings = JSON.parse(result[0].setting_value);
      }
    } catch (error) {
      logger.warn('获取现有安全设置失败', { error: error.message });
    }

    // 构建设置对象（与.env文件字段对齐）
    const securitySettings = {
      jwtSecret: (jwtSecret && jwtSecret !== '******') ? jwtSecret : existingSettings.jwtSecret || 'default-secret-key',
      jwtExpiresIn: jwtExpiresIn || existingSettings.jwtExpiresIn || '24h',
      jwtAdminExpiresIn: jwtAdminExpiresIn || existingSettings.jwtAdminExpiresIn || '12h',
      accessTokenSecret: (accessTokenSecret && accessTokenSecret !== '******') ? accessTokenSecret : existingSettings.accessTokenSecret || 'default-access-token-secret',
      refreshTokenSecret: (refreshTokenSecret && refreshTokenSecret !== '******') ? refreshTokenSecret : existingSettings.refreshTokenSecret || 'default-refresh-secret',
      passwordMinLength: Number(passwordMinLength),
      enableCaptcha: Boolean(enableCaptcha),
      maxLoginAttempts: Number(maxLoginAttempts),
      lockTime: Number(lockTime)
    };

    // 将设置保存到数据库
    const settingsJson = JSON.stringify(securitySettings);
    
    // 检查设置是否已存在
    const existingSettingsResult = await query(
      'SELECT id FROM system_settings WHERE setting_key = ?',
      ['security_settings']
    );

    if (existingSettingsResult.length > 0) {
      // 更新现有设置
      await query(
        'UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?',
        [settingsJson, 'security_settings']
      );
    } else {
      // 插入新设置
      await query(
        'INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
        ['security_settings', settingsJson]
      );
    }

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_UPDATE_SECURITY_SETTINGS',
      description: '管理员更新安全设置',
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: { jwtExpiresIn, jwtAdminExpiresIn, passwordMinLength, enableCaptcha, maxLoginAttempts, lockTime }
    });

    return {
      success: true,
      message: '安全设置更新成功'
    };

  } catch (error: any) {
    logger.error('更新安全设置失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
