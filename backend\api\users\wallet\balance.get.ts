/**
 * 获取用户钱包余额接口
 * GET /api/users/wallet/balance
 */

import { query } from '~/utils/database'
import { verifyUserAccessToken } from '~/utils/auth'

export default defineEventHandler(async (event) => {
  try {
    // 验证用户认证
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户认证失败，请重新登录'
      });
    }

    const userId = userPayload.id;

    // 获取用户资产信息，如果不存在则创建
    let assetRows = await query(`
      SELECT
        shells_balance,
        diamonds_balance,
        total_invested_shells,
        total_earned_diamonds,
        frozen_shells,
        frozen_diamonds
      FROM user_assets
      WHERE user_id = ?
    `, [userId]);

    if (!assetRows || assetRows.length === 0) {
      // 用户资产记录不存在，创建初始记录
      console.log(`用户 ${userId} 资产记录不存在，创建初始记录`);
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId]);

      // 重新查询创建的记录
      assetRows = await query(`
        SELECT
          shells_balance,
          diamonds_balance,
          total_invested_shells,
          total_earned_diamonds,
          frozen_shells,
          frozen_diamonds
        FROM user_assets
        WHERE user_id = ?
      `, [userId]);
    }

    const asset = assetRows[0];

    return {
      success: true,
      data: {
        shellsBalance: parseFloat(asset.shells_balance),
        diamondsBalance: parseFloat(asset.diamonds_balance),
        totalInvestedShells: parseFloat(asset.total_invested_shells),
        totalEarnedDiamonds: parseFloat(asset.total_earned_diamonds),
        frozenShells: parseFloat(asset.frozen_shells),
        frozenDiamonds: parseFloat(asset.frozen_diamonds),
        availableShells: parseFloat(asset.shells_balance) - parseFloat(asset.frozen_shells)
      }
    };

  } catch (error: any) {
    console.error('获取钱包余额失败:', error);
    
    return {
      success: false,
      message: '获取钱包余额失败',
      error: error.message
    };
  }
});
