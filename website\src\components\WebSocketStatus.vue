<template>
  <div class="websocket-status" v-if="showStatus">
    <div class="status-indicator" :class="statusClass">
      <div class="status-dot"></div>
      <span class="status-text">{{ statusText }}</span>
    </div>
    
    <!-- 开发模式下显示详细信息 -->
    <div v-if="isDev && isConnected" class="status-details">
      <div class="detail-item">
        <span>连接时间:</span>
        <span>{{ connectedTime }}</span>
      </div>
      <div class="detail-item">
        <span>消息数:</span>
        <span>{{ messageCount }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { getWebSocketClient } from '../utils/websocket'

// 状态
const isConnected = ref(false)
const connectedTime = ref('')
const messageCount = ref(0)
const isDev = ref(import.meta.env.MODE === 'development')

// 计算属性
const showStatus = computed(() => {
  const authStore = useAuthStore()
  return authStore.isLoggedIn && isDev.value
})

const statusClass = computed(() => ({
  'status-connected': isConnected.value,
  'status-disconnected': !isConnected.value
}))

const statusText = computed(() => {
  return isConnected.value ? 'WebSocket已连接' : 'WebSocket未连接'
})

// 检查WebSocket状态
const checkWebSocketStatus = () => {
  const client = getWebSocketClient()
  if (client) {
    isConnected.value = client.isConnected
    if (isConnected.value && !connectedTime.value) {
      connectedTime.value = new Date().toLocaleTimeString()
    }
  } else {
    isConnected.value = false
    connectedTime.value = ''
  }
}

// 监听WebSocket事件
const handleWebSocketMessage = () => {
  messageCount.value++
}

let statusCheckInterval: number | null = null

onMounted(() => {
  // 定期检查WebSocket状态
  statusCheckInterval = window.setInterval(checkWebSocketStatus, 1000)
  
  // 监听WebSocket消息事件
  window.addEventListener('websocket:message', handleWebSocketMessage)
  
  // 初始检查
  checkWebSocketStatus()
})

onUnmounted(() => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
  }
  window.removeEventListener('websocket:message', handleWebSocketMessage)
})
</script>

<style scoped>
.websocket-status {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.status-connected .status-dot {
  background-color: #52c41a;
  box-shadow: 0 0 4px rgba(82, 196, 26, 0.5);
}

.status-disconnected .status-dot {
  background-color: #ff4d4f;
  box-shadow: 0 0 4px rgba(255, 77, 79, 0.5);
}

.status-text {
  color: #333;
  font-weight: 500;
}

.status-details {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  color: #666;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item span:first-child {
  font-weight: 500;
}

.detail-item span:last-child {
  color: #333;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .websocket-status {
    background: rgba(30, 30, 30, 0.95);
    border-color: #404040;
  }
  
  .status-text {
    color: #fff;
  }
  
  .detail-item {
    color: #ccc;
  }
  
  .detail-item span:last-child {
    color: #fff;
  }
  
  .status-details {
    border-top-color: #404040;
  }
}
</style>
