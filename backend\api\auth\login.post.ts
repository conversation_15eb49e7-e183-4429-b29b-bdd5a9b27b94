import {
  findUserByCredentials,
  verifyPassword,
  generateUserAccessToken,
  generateUserRefreshToken,
  updateUserLastLogin
} from '~/utils/auth';
import {
  useResponseSuccess,
  useResponseError,
  validationErrorResponse,
  serverErrorResponse
} from '~/utils/response';
import { logger, logAuditAction, extractAuditInfo } from '~/utils/logger';
import { setRefreshTokenCookie } from '~/utils/cookie-utils';

export default defineEventHandler(async (event) => {
  try {
    console.log('=== 后端登录调试日志 ===');
    console.log('1. 收到登录请求');

    // 获取请求体
    const body = await readBody(event);
    const { email, phone, password } = body;

    console.log('2. 解析请求数据');
    console.log('邮箱:', email);
    console.log('手机号:', phone);
    console.log('密码长度:', password ? password.length : 0);

    // 验证输入参数 - 只支持邮箱或手机号登录
    if ((!email && !phone) || !password) {
      console.log('3. 验证失败 - 缺少必要参数');
      return validationErrorResponse(
        event,
        '请提供邮箱或手机号，以及密码',
        {
          email: !email && !phone ? '请提供邮箱或手机号' : null,
          phone: !email && !phone ? '请提供邮箱或手机号' : null,
          password: !password ? '密码不能为空' : null
        }
      );
    }

    console.log('3. 输入验证通过');

    // 查找C端用户（不包括管理员）- 使用邮箱或手机号
    console.log('4. 查找用户');
    const identifier = email || phone;
    const user = await findUserByCredentials(identifier);
    console.log('用户查找结果:', user ? '找到用户' : '用户不存在');

    if (!user) {
      console.log('5. 登录失败 - 用户不存在');
      // 记录登录失败日志
      await logAuditAction({
        action: 'USER_LOGIN_FAILED',
        description: `C端用户不存在: ${identifier}`,
        ...extractAuditInfo(event)
      });

      return useResponseError('邮箱/手机号或密码错误');
    }

    // 验证密码 - C端用户使用password_hash字段
    console.log('5. 验证密码');
    console.log('用户密码字段情况:', {
      hasPasswordHash: !!user.password_hash,
      passwordHashLength: user.password_hash ? user.password_hash.length : 0
    });

    // C端用户使用password_hash字段存储密码
    const storedPasswordHash = user.password_hash || '';
    console.log('使用的密码字段: password_hash');
    console.log('存储的密码哈希:', storedPasswordHash);

    if (!storedPasswordHash) {
      console.log('6. 登录失败 - 用户没有设置密码');
      return useResponseError('用户密码未设置，请联系管理员');
    }

    const isValidPassword = await verifyPassword(password, storedPasswordHash);
    console.log('密码验证结果:', isValidPassword ? '密码正确' : '密码错误');

    if (!isValidPassword) {
      console.log('6. 登录失败 - 密码错误');
      // 记录密码错误日志
      await logAuditAction({
        userId: user.id,
        username: user.username,
        userType: 'user',
        action: 'USER_LOGIN_WRONG_PASSWORD',
        description: 'C端用户密码错误尝试',
        ...extractAuditInfo(event)
      });

      return useResponseError('用户名或密码错误');
    }

    console.log('6. 密码验证通过，生成令牌');

    // 生成用户令牌
    const accessToken = await generateUserAccessToken(user);
    const refreshToken = await generateUserRefreshToken(user);
    console.log('7. 令牌生成成功');

    // 设置刷新令牌到Cookie
    setRefreshTokenCookie(event, refreshToken);

    // 更新最后登录时间
    await updateUserLastLogin(user.id);
    console.log('8. 更新最后登录时间');

    // 记录登录成功日志
    await logAuditAction({
      userId: user.id,
      username: user.username,
      userType: 'user',
      action: 'USER_LOGIN_SUCCESS',
      description: 'C端用户登录成功',
      ...extractAuditInfo(event)
    });

    logger.info('C端用户登录成功', {
      userId: user.id,
      username: user.username
    });

    console.log('9. 准备返回用户信息');

    // 返回用户信息和令牌
    const { password: _, ...userWithoutPassword } = user;
    const responseData = {
      token: accessToken,
      user: {
        ...userWithoutPassword,
        accessToken,
        realName: user.username,
        roles: ['user'],
        homePath: '/workspace'
      }
    };

    console.log('10. 登录成功，返回数据');
    console.log('返回的用户信息:', responseData.user);

    return useResponseSuccess(responseData, '登录成功');

  } catch (error) {
    console.log('=== 后端登录错误 ===');
    console.error('登录过程中发生错误:', error);
    logger.error('登录过程中发生错误', { error: error.message });

    // 记录系统错误日志
    await logAuditAction({
      action: 'USER_LOGIN_ERROR',
      description: `C端用户登录系统错误: ${error.message}`,
      ...extractAuditInfo(event)
    });

    return serverErrorResponse(event, '登录失败，请稍后重试');
  }
});
