/**
 * 设备管理工具函数
 */

/**
 * 生成设备指纹
 */
export const generateDeviceFingerprint = (): string => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx!.textBaseline = 'top'
  ctx!.font = '14px Arial'
  ctx!.fillText('Device fingerprint', 2, 2)
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|')
  
  // 简单哈希
  let hash = 0
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  return Math.abs(hash).toString(36)
}

/**
 * 获取设备信息
 */
export const getDeviceInfo = () => {
  const ua = navigator.userAgent
  let deviceType = 'Unknown'
  let browser = 'Unknown'
  let os = 'Unknown'
  
  // 检测设备类型
  if (/Mobile|Android|iPhone|iPad/.test(ua)) {
    deviceType = 'Mobile'
  } else if (/Tablet|iPad/.test(ua)) {
    deviceType = 'Tablet'
  } else {
    deviceType = 'Desktop'
  }
  
  // 检测浏览器
  if (ua.includes('Chrome')) browser = 'Chrome'
  else if (ua.includes('Firefox')) browser = 'Firefox'
  else if (ua.includes('Safari')) browser = 'Safari'
  else if (ua.includes('Edge')) browser = 'Edge'
  
  // 检测操作系统
  if (ua.includes('Windows')) os = 'Windows'
  else if (ua.includes('Mac')) os = 'macOS'
  else if (ua.includes('Linux')) os = 'Linux'
  else if (ua.includes('Android')) os = 'Android'
  else if (ua.includes('iOS')) os = 'iOS'
  
  return {
    deviceId: generateDeviceFingerprint(),
    deviceType,
    browser,
    os,
    userAgent: ua,
    timestamp: new Date().toISOString()
  }
}

/**
 * 保存设备信息到本地存储
 */
export const saveDeviceInfo = () => {
  const deviceInfo = getDeviceInfo()
  localStorage.setItem('deviceInfo', JSON.stringify(deviceInfo))
  return deviceInfo
}

/**
 * 获取保存的设备信息
 */
export const getSavedDeviceInfo = () => {
  try {
    const saved = localStorage.getItem('deviceInfo')
    return saved ? JSON.parse(saved) : null
  } catch (error) {
    console.error('获取设备信息失败:', error)
    return null
  }
}

/**
 * 检查是否为新设备
 */
export const isNewDevice = (): boolean => {
  const saved = getSavedDeviceInfo()
  const current = getDeviceInfo()
  
  return !saved || saved.deviceId !== current.deviceId
}

/**
 * 设备登录记录
 */
export interface DeviceLoginRecord {
  deviceId: string
  deviceType: string
  browser: string
  os: string
  loginTime: string
  lastActiveTime: string
  location?: string
  isCurrentDevice: boolean
}

/**
 * 获取设备登录历史
 */
export const getDeviceLoginHistory = (): DeviceLoginRecord[] => {
  try {
    const history = localStorage.getItem('deviceLoginHistory')
    return history ? JSON.parse(history) : []
  } catch (error) {
    console.error('获取设备登录历史失败:', error)
    return []
  }
}

/**
 * 添加设备登录记录
 */
export const addDeviceLoginRecord = (location?: string) => {
  const deviceInfo = getDeviceInfo()
  const history = getDeviceLoginHistory()
  
  // 查找是否已存在该设备的记录
  const existingIndex = history.findIndex(record => record.deviceId === deviceInfo.deviceId)
  
  const record: DeviceLoginRecord = {
    deviceId: deviceInfo.deviceId,
    deviceType: deviceInfo.deviceType,
    browser: deviceInfo.browser,
    os: deviceInfo.os,
    loginTime: existingIndex === -1 ? deviceInfo.timestamp : history[existingIndex].loginTime,
    lastActiveTime: deviceInfo.timestamp,
    location,
    isCurrentDevice: true
  }
  
  // 将其他设备标记为非当前设备
  history.forEach(h => h.isCurrentDevice = false)
  
  if (existingIndex !== -1) {
    history[existingIndex] = record
  } else {
    history.unshift(record)
  }
  
  // 只保留最近10个设备记录
  const limitedHistory = history.slice(0, 10)
  
  localStorage.setItem('deviceLoginHistory', JSON.stringify(limitedHistory))
  return record
}

/**
 * 清除设备登录历史
 */
export const clearDeviceLoginHistory = () => {
  localStorage.removeItem('deviceLoginHistory')
}

/**
 * 移除特定设备记录
 */
export const removeDeviceRecord = (deviceId: string) => {
  const history = getDeviceLoginHistory()
  const filteredHistory = history.filter(record => record.deviceId !== deviceId)
  localStorage.setItem('deviceLoginHistory', JSON.stringify(filteredHistory))
}
